// photos_tab_content.dart
// This screen displays photo feed

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/scroll_util.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/widgets/masonry/photo_masonry_item.dart';
import 'package:portraitmode/photo/widgets/photo_detail_screen.dart';
import 'package:portraitmode/search/dto/search_data.dart';
import 'package:portraitmode/search/misc.dart';
import 'package:portraitmode/search/providers/search_photos_data_provider.dart';
import 'package:portraitmode/search/providers/search_photos_provider.dart';

class PhotosTabContent extends ConsumerStatefulWidget {
  const PhotosTabContent({
    super.key,
    required this.searchData,
    required this.keyword,
    required this.dataList,
    this.onPhotoItemTwoFingersOn,
    this.onPhotoItemTwoFingersOff,
  });

  final PhotosSearchData searchData;
  final String keyword;
  final List<PhotoData> dataList;
  final Function? onPhotoItemTwoFingersOn;
  final Function? onPhotoItemTwoFingersOff;

  @override
  PhotosTabContentState createState() => PhotosTabContentState();
}

class PhotosTabContentState extends ConsumerState<PhotosTabContent> {
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();
  late final int _profileId;

  final _photoListService = PhotoListService();
  final int _loadMorePerPage = LoadMoreConfig.gridItemsPerPage;
  bool _isFetchingData = false;

  @override
  void initState() {
    super.initState();

    _profileId = LocalUserService.userId ?? 0;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _loadMore(false);
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  double _getLoadMoreTriggerPoint(double maxExtent) {
    // Calculate how much of the content should remain when triggering load more
    double remainingContent = maxExtent * LoadMoreConfig.tresholdPercentage;

    // Apply min/max constraints
    remainingContent = remainingContent.clamp(
      LoadMoreConfig.minTreshold,
      LoadMoreConfig.maxTreshold,
    );

    // Return the scroll position that should trigger load more
    return maxExtent - remainingContent;
  }

  bool _canLoadMore() {
    return !_isFetchingData && !widget.searchData.loadMoreEndReached;
  }

  @override
  Widget build(BuildContext context) {
    // pmLog('build screen: PhotosTabContent');

    return Container(
      constraints: const BoxConstraints(maxWidth: 768.0),
      child: RefreshIndicator(
        key: _refreshIndicatorKey,
        color: context.colors.brandColor,
        elevation: 0.0,
        onRefresh: _handleRefresh,
        child: NotificationListener<ScrollNotification>(
          onNotification: (ScrollNotification scrollInfo) {
            final double triggerPoint = _getLoadMoreTriggerPoint(
              scrollInfo.metrics.maxScrollExtent,
            );

            // Handle load more when scrolling reaches the trigger point
            if (scrollInfo.metrics.pixels >= triggerPoint) {
              if (_canLoadMore()) _loadMore(false);
            }

            return false;
          },
          child: MasonryGridView.count(
            padding: const EdgeInsets.all(8.0),
            crossAxisCount: 2,
            mainAxisSpacing: 8.0,
            crossAxisSpacing: 8.0,
            cacheExtent: getVerticalScrollCacheExtent(context),
            itemCount: widget.dataList.length,
            itemBuilder: (BuildContext context, int index) {
              if (index >= widget.dataList.length) {
                return const SizedBox.shrink();
              }

              return PhotoMasonryItem(
                key: ValueKey(widget.dataList[index].id),
                index: index,
                photo: widget.dataList[index],
                isOwnProfile: widget.dataList[index].authorId == _profileId,
                screenName: 'photo_search_screen',
                onTwoFingersOn: () {
                  if (!mounted) return;

                  if (widget.onPhotoItemTwoFingersOn != null) {
                    widget.onPhotoItemTwoFingersOn!();
                  }
                },
                onTwoFingersOff: () {
                  if (!mounted) return;

                  if (widget.onPhotoItemTwoFingersOff != null) {
                    widget.onPhotoItemTwoFingersOff!();
                  }
                },
                onPhotoTap: () => _handlePhotoTap(widget.dataList[index]),
              );
            },
          ),
        ),
      ),
    );
  }

  void _handlePhotoTap(PhotoData photo) {
    FocusScope.of(context).unfocus();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PhotoDetailScreen(
          photo: photo,
          originScreenName: 'photo_search_screen',
        ),
      ),
    );
  }

  bool _isFirstLoad() {
    return widget.searchData.loadMoreLastId == -1 ||
        widget.searchData.loadMoreLastId == 0;
  }

  Future<void> _handleRefresh() async {
    _loadMore(true);
  }

  Future<void> _loadMore(bool isRefresh) async {
    _isFetchingData = true;

    if (!isRefresh && mounted) {
      SearchLoadMoreNotification(
        photoSearchScreen: _isFirstLoad()
            ? SearchScreenLoadMoreStatus.doingFirstLoadMore
            : SearchScreenLoadMoreStatus.doingLoadMore,
      ).dispatch(context);
    }

    late PhotoListResponse response;

    // pmLog('The photos search keyword is: "${widget.keyword}"');

    if (widget.keyword.isNotEmpty) {
      response = await _photoListService.search(
        keyword: widget.keyword,
        limit: _loadMorePerPage,
        lastId: isRefresh ? 0 : widget.searchData.loadMoreLastId,
      );
    } else {
      response = await _photoListService.fetch(
        limit: _loadMorePerPage,
        lastId: isRefresh ? 0 : widget.searchData.loadMoreLastId,
      );
    }

    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    _handlePhotoListResponse(response, isRefresh);

    if (!isRefresh && mounted) {
      SearchLoadMoreNotification(
        photoSearchScreen: SearchScreenLoadMoreStatus.idle,
      ).dispatch(context);
    }

    _isFetchingData = false;
  }

  void _handlePhotoListResponse(PhotoListResponse response, bool isRefresh) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    final searchPhotosReactiveService = ref.read(
      searchPhotosReactiveServiceProvider,
    );

    final bool isFirstLoad = _isFirstLoad();

    if (response.data.isEmpty) {
      if (isRefresh || isFirstLoad) {
        searchPhotosReactiveService.clear();
      }

      ref.read(searchPhotosDataProvider.notifier).setLoadMoreEndReached(true);
      return;
    }

    if (response.data.length < _loadMorePerPage) {
      ref.read(searchPhotosDataProvider.notifier).setLoadMoreEndReached(true);
    } else {
      ref.read(searchPhotosDataProvider.notifier).setLoadMoreEndReached(false);
    }

    ref
        .read(searchPhotosDataProvider.notifier)
        .setLoadMoreLastId(response.data.last.id);

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh || isFirstLoad) {
      searchPhotosReactiveService.replaceAll(response.data);
    } else {
      searchPhotosReactiveService.addItems(response.data);
    }
  }
}
