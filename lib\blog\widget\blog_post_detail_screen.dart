import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/config/url.dart';
import 'package:portraitmode/app/widgets/pm_network_image.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/blog/dto/blog_post_data.dart';
import 'package:portraitmode/blog/services/blog_post_service.dart';

class BlogPostDetailScreen extends StatefulWidget {
  final BlogPostData post;
  final bool isLoading;

  const BlogPostDetailScreen({
    super.key,
    required this.post,
    this.isLoading = true,
  });

  @override
  BlogPostDetailScreenState createState() => BlogPostDetailScreenState();
}

class BlogPostDetailScreenState extends State<BlogPostDetailScreen> {
  final _scrollController = ScrollController();
  String _postContent = '';
  late bool _isLoading;

  @override
  void initState() {
    _isLoading = widget.isLoading;
    _fetchData();
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bool showFeaturedImage =
        widget.post.featuredImage != null &&
        widget.post.featuredImage!.mediumUrl.isNotEmpty;

    return Scaffold(
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            double containerWidth = 0;
            double imageWidth = 0;
            double imageHeight = 0;
            double computedImageHeight = 0;

            if (showFeaturedImage) {
              containerWidth = constraints.maxWidth;

              imageWidth = showFeaturedImage
                  ? widget.post.featuredImage!.width.toDouble()
                  : 0;

              imageHeight = showFeaturedImage
                  ? widget.post.featuredImage!.height.toDouble()
                  : 0;

              computedImageHeight = ImageSize.computedHeight(
                parentWidth: containerWidth,
                imageWidth: imageWidth,
                imageHeight: imageHeight,
              );
            }

            return CustomScrollView(
              controller: _scrollController,
              slivers: [
                PmSliverAppBar(
                  scrollController: _scrollController,
                  automaticallyImplyLeading: true,
                  actions: const [],
                ),
                if (showFeaturedImage)
                  SliverAppBar(
                    automaticallyImplyLeading: false,
                    expandedHeight: computedImageHeight,
                    flexibleSpace: FlexibleSpaceBar(
                      background: Container(
                        decoration: BoxDecoration(
                          color: context.colors.lightColor,
                          image: DecorationImage(
                            image: PmNetworkImageProvider(
                              widget.post.featuredImage!.mediumUrl,
                            ).imageProvider,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                  ),
                SliverList(
                  delegate: SliverChildListDelegate([
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 20.0,
                        bottom: 40.0,
                        left: ScreenStyleConfig.horizontalPadding * 2,
                        right: ScreenStyleConfig.horizontalPadding * 2,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.post.title,
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 10.0),
                          if (_isLoading)
                            Column(
                              children: [
                                Container(
                                  height: 150,
                                  color: context.colors.lightColor,
                                ),
                                const SizedBox(height: 10.0),
                                Container(
                                  height: 150,
                                  color: context.colors.lightColor,
                                ),
                                const SizedBox(height: 10.0),
                                Container(
                                  height: 150,
                                  color: context.colors.lightColor,
                                ),
                                const SizedBox(height: 10.0),
                                Container(
                                  height: 150,
                                  color: context.colors.lightColor,
                                ),
                              ],
                            ),
                          if (!_isLoading)
                            HtmlWidget(
                              _postContent,
                              baseUrl: Uri.parse(URL.siteUrl),
                              // The `colors` param is only available in our fork
                              // of the `flutter_widget_from_html_core` package.
                              // colors: HtmlWidgetColors(
                              //   hr: context.colors.dividerColor,
                              // ),
                            ),
                        ],
                      ),
                    ),
                  ]),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Future<void> _fetchData() async {
    if (!_isLoading) {
      if (mounted) {
        setState(() {
          _isLoading = true;
        });
      }
    }

    final response = await BlogPostService().find(widget.post.id);

    if (mounted) {
      setState(() {
        _isLoading = false;

        if (response.success && response.data != null) {
          _postContent = response.data!.content;
        }
      });
    }
  }
}
