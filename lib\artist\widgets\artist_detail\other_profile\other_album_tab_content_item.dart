// other_album_tab_content_item.dart
// This screen displays photo feed

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:portraitmode/album/dto/album_data.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/scroll_util.dart';
import 'package:portraitmode/artist/http_responses/artist_response.dart';
import 'package:portraitmode/artist/misc.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';
import 'package:portraitmode/artist/services/artist_service.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/widgets/masonry/photo_masonry_item.dart';
import 'package:portraitmode/photo/widgets/photo_detail_screen.dart';

class OtherAlbumTabContentItem extends ConsumerStatefulWidget {
  final ValueNotifier<bool> loadMoreNotifier;
  final int artistId;
  final AlbumData album;

  const OtherAlbumTabContentItem({
    super.key,
    required this.loadMoreNotifier,
    required this.artistId,
    required this.album,
  });

  @override
  OtherAlbumTabContentItemState createState() =>
      OtherAlbumTabContentItemState();
}

class OtherAlbumTabContentItemState
    extends ConsumerState<OtherAlbumTabContentItem>
    with AutomaticKeepAliveClientMixin<OtherAlbumTabContentItem> {
  final _artistService = ArtistService();
  final _photoListService = PhotoListService();
  final ValueNotifier<List<PhotoData>> _photoListNotifier = ValueNotifier([]);

  final int _loadMorePerPage = LoadMoreConfig.gridItemsPerPage;
  int _loadMoreLastId = 0;
  bool _loadMoreEndReached = false;
  bool _isLoadingMore = false;

  @override
  bool wantKeepAlive = true;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _triggerLoadMore();
    });
  }

  @override
  void dispose() {
    _photoListNotifier.dispose();
    super.dispose();
  }

  double _getLoadMoreTriggerPoint(double maxExtent) {
    // Calculate how much of the content should remain when triggering load more
    double remainingContent = maxExtent * LoadMoreConfig.tresholdPercentage;

    // Apply min/max constraints
    remainingContent = remainingContent.clamp(
      LoadMoreConfig.minTreshold,
      LoadMoreConfig.maxTreshold,
    );

    // Return the scroll position that should trigger load more
    return maxExtent - remainingContent;
  }

  void _onScroll(ScrollMetrics metrics) {
    if (!_canLoadMore()) return;

    double triggerPoint = _getLoadMoreTriggerPoint(metrics.maxScrollExtent);

    // Handle load more when scrolling reaches the trigger point
    if (metrics.pixels >= triggerPoint) {
      _triggerLoadMore();
    }
  }

  bool _canLoadMore() {
    return !_isLoadingMore && !_loadMoreEndReached;
  }

  /// Trigger load more.
  ///
  /// The `_canLoadMore` checking should be in the caller, not here.
  void _triggerLoadMore() async {
    _isLoadingMore = true;

    AlbumLoadMoreNotification(
      albums: {widget.album.slug: true},
    ).dispatch(context);

    await _loadMore();

    if (mounted) {
      AlbumLoadMoreNotification(
        albums: {widget.album.slug: false},
      ).dispatch(context);
    }

    _isLoadingMore = false;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification scrollInfo) {
        _onScroll(scrollInfo.metrics);

        return false; // Allow the notification to continue
      },
      child: RefreshIndicator(
        color: context.colors.brandColor,
        elevation: 0.0,
        onRefresh: _handleRefresh,
        child: ValueListenableBuilder(
          valueListenable: _photoListNotifier,
          builder: (context, photoList, child) {
            return MasonryGridView.count(
              cacheExtent: getVerticalScrollCacheExtent(context),
              padding: const EdgeInsets.all(8.0),
              crossAxisCount: 2,
              mainAxisSpacing: 8.0,
              crossAxisSpacing: 8.0,
              itemCount: photoList.length,
              itemBuilder: (BuildContext context, int index) {
                if (index >= photoList.length) {
                  return const SizedBox.shrink();
                }

                return PhotoMasonryItem(
                  key: ValueKey(photoList[index].id),
                  index: index,
                  photo: photoList[index],
                  isOwnProfile: false,
                  screenName: 'other_profile',
                  onPhotoTap: () => _handlePhotoTap(photoList[index]),
                );
              },
            );
          },
        ),
      ),
    );
  }

  void _handlePhotoTap(PhotoData photo) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            PhotoDetailScreen(photo: photo, originScreenName: 'other_profile'),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    _loadMoreLastId = 0;
    _loadMoreEndReached = false;

    final List<dynamic> responses = await Future.wait([
      _artistService.find(widget.artistId),
      _photoListService.fetch(
        albumSlug: widget.album.slug,
        limit: _loadMorePerPage,
        lastId: _loadMoreLastId,
        artistId: widget.artistId,
      ),
    ]);

    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    final ArtistResponse artistResponse = responses[0];

    if (!artistResponse.success) {
      if (authUtil.errorCodeRequiresLogin(artistResponse.errorCode)) {
        if (mounted) {
          showSessionEndedDialog(context, ref);
        }
      }
    }

    if (artistResponse.success && artistResponse.data != null) {
      ref.read(artistStoreProvider.notifier).updateItem(artistResponse.data!);
    }

    final PhotoListResponse photoListResponse = responses[1];

    _handlePhotoListResponse(photoListResponse, true);
  }

  Future<void> _loadMore() async {
    final PhotoListResponse response = await _photoListService.fetch(
      albumSlug: widget.album.slug,
      limit: _loadMorePerPage,
      lastId: _loadMoreLastId,
      artistId: widget.artistId,
    );

    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    _handlePhotoListResponse(response, false);
  }

  void _handlePhotoListResponse(PhotoListResponse response, bool isRefresh) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      }

      widget.loadMoreNotifier.value = false;
      return;
    }

    final bool isFirstLoad = _loadMoreLastId == 0 || _loadMoreLastId == -1;

    if (response.data.isEmpty) {
      _loadMoreEndReached = true;

      if (isRefresh || isFirstLoad) {
        _photoListNotifier.value = [];
      }

      return;
    }

    if (response.data.length < _loadMorePerPage) {
      _loadMoreEndReached = true;
    }

    _loadMoreLastId = response.data.last.id;

    // Sort response.data (photo list) before consuming it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    ref.read(photoStoreProvider.notifier).updateItems(response.data);

    if (isRefresh || isFirstLoad) {
      _photoListNotifier.value = response.data;
    } else {
      _photoListNotifier.value = [
        ..._photoListNotifier.value,
        ...response.data,
      ];
    }
  }
}
