import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

final class PotdPhotoIdsNotifier extends IdListNotifier {}

final potdPhotoIdsProvider =
    NotifierProvider.autoDispose<PotdPhotoIdsNotifier, List<int>>(
      PotdPhotoIdsNotifier.new,
    );

final potdPhotosProvider = Provider.autoDispose<List<PhotoData>>((ref) {
  final ids = ref.watch(potdPhotoIdsProvider);
  if (ids.isEmpty) return <PhotoData>[];

  return ref.read(photoStoreProvider.notifier).getItems(ids);
});

/// High-level service for managing potd photos reactivity.
///
/// This is the recommended way to manage latest photos.
final class PotdPhotosReactiveService {
  const PotdPhotosReactiveService(this.ref);

  final Ref ref;

  /// Get local photo list
  List<PhotoData> getAll() {
    return ref.read(potdPhotosProvider);
  }

  /// Get local id list
  List<int> getAllIds() {
    return ref.read(potdPhotoIdsProvider);
  }

  /// Add a new photo (adds to both global store and local ids)
  void addItem(PhotoData photo) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItem(photo);

    // Add to local id list
    ref.read(potdPhotoIdsProvider.notifier).addItem(photo.id);
  }

  /// Add multiple photos
  void addItems(List<PhotoData> photos) {
    if (photos.isEmpty) return;

    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Add to local id list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(potdPhotoIdsProvider.notifier).addItems(photoIds);
  }

  /// Remove a local photo
  void remove(int photoId) {
    ref.read(potdPhotoIdsProvider.notifier).removeItem(photoId);
  }

  /// Remove multiple local photos
  void removeItems(List<int> photoIds) {
    if (photoIds.isEmpty) return;

    // Remove from local id list
    ref.read(potdPhotoIdsProvider.notifier).removeItems(photoIds);
  }

  /// Update a photo in the store (automatically reflects in local id list)
  void updateItem(PhotoData updatedPhoto) {
    ref.read(photoStoreProvider.notifier).updateItem(updatedPhoto);
  }

  /// Update multiple photos in the store
  void updateItems(List<PhotoData> updatedPhotos) {
    ref.read(photoStoreProvider.notifier).updateItems(updatedPhotos);
  }

  /// Replace all local photos
  void replaceAll(List<PhotoData> photos) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Replace local id list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(potdPhotoIdsProvider.notifier).replaceAll(photoIds);
  }

  // Clear the local id list
  void clear() {
    ref.read(potdPhotoIdsProvider.notifier).clear();
  }

  /// Get local id list count
  int get count => ref.read(potdPhotoIdsProvider.notifier).length;

  /// Check if local id list is empty
  bool get isEmpty => ref.read(potdPhotoIdsProvider.notifier).isEmpty;

  /// Check if local id list is not empty
  bool get isNotEmpty => ref.read(potdPhotoIdsProvider.notifier).isNotEmpty;
}

/// Provider for the PotdPhotosReactiveService.
final potdPhotosReactiveServiceProvider =
    Provider.autoDispose<PotdPhotosReactiveService>((ref) {
      return PotdPhotosReactiveService(ref);
    });
