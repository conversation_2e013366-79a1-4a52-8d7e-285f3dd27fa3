// cameras_tab_content.dart
// This screen displays camera feed

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/scroll_util.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/camera/dto/camera_data.dart';
import 'package:portraitmode/camera/http_responses/camera_list_response.dart';
import 'package:portraitmode/camera/services/camera_list_service.dart';
import 'package:portraitmode/camera/widgets/camera_list_item.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/search/dto/search_data.dart';
import 'package:portraitmode/search/misc.dart';
import 'package:portraitmode/search/providers/search_cameras_data_provider.dart';
import 'package:portraitmode/search/providers/search_cameras_provider.dart';

class CamerasTabContent extends ConsumerStatefulWidget {
  const CamerasTabContent({
    super.key,
    required this.searchData,
    required this.keyword,
    required this.dataList,
  });

  final CamerasSearchData searchData;
  final String keyword;
  final List<CameraData> dataList;

  @override
  CamerasTabContentState createState() => CamerasTabContentState();
}

class CamerasTabContentState extends ConsumerState<CamerasTabContent> {
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  final _cameraListService = CameraListService();

  /// Number of load more per page.
  ///
  /// We will exclude cameras with photos less than 3.
  /// Currently, there's no straight-forward / "out of the box" way to do this.
  ///
  /// That's why, in the camera query, we will exclude cameras with photos less than 3
  /// which means, the photos returned could be less than `limit`.
  ///
  /// To prevent a very low number of photos returned,
  /// we set the `limit` LoadMoreConfig.gridItemsPerPage * 2.
  final int _loadMorePerPage = LoadMoreConfig.gridItemsPerPage * 2;
  bool _isFetchingData = false;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _loadMore(false);
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  double _getLoadMoreTriggerPoint(double maxExtent) {
    // Calculate how much of the content should remain when triggering load more
    double remainingContent = maxExtent * LoadMoreConfig.tresholdPercentage;

    // Apply min/max constraints
    remainingContent = remainingContent.clamp(
      LoadMoreConfig.minTreshold,
      LoadMoreConfig.maxTreshold,
    );

    // Return the scroll position that should trigger load more
    return maxExtent - remainingContent;
  }

  bool _canLoadMore() {
    return !_isFetchingData && !widget.searchData.loadMoreEndReached;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: const BoxConstraints(maxWidth: 768.0),
      child: RefreshIndicator(
        key: _refreshIndicatorKey,
        color: context.colors.brandColor,
        elevation: 0.0,
        onRefresh: _handleRefresh,
        child: NotificationListener<ScrollNotification>(
          onNotification: (ScrollNotification scrollInfo) {
            final double triggerPoint = _getLoadMoreTriggerPoint(
              scrollInfo.metrics.maxScrollExtent,
            );

            // Handle load more when scrolling reaches the trigger point
            if (scrollInfo.metrics.pixels >= triggerPoint) {
              if (_canLoadMore()) _loadMore(false);
            }

            return false;
          },
          child: GridView.builder(
            // Remove the controller - let NestedScrollView handle scrolling
            padding: const EdgeInsets.all(8.0),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisSpacing: 8.0,
              crossAxisSpacing: 8.0,
              childAspectRatio: 1.8,
            ),
            cacheExtent: getVerticalScrollCacheExtent(context),
            itemCount: widget.dataList.length,
            itemBuilder: (BuildContext context, int index) {
              return CameraListItem(
                key: ValueKey(widget.dataList[index].id),
                camera: widget.dataList[index],
              );
            },
          ),
        ),
      ),
    );
  }

  bool _isFirstLoad() {
    return widget.searchData.loadMoreLastId == -1 ||
        widget.searchData.loadMoreLastId == 0 ||
        widget.searchData.loadMoreLastId == null;
  }

  Future<void> _handleRefresh() async {
    _loadMore(true);
  }

  Future<void> _loadMore(bool isRefresh) async {
    _isFetchingData = true;

    if (!isRefresh && mounted) {
      SearchLoadMoreNotification(
        cameraSearchScreen: _isFirstLoad()
            ? SearchScreenLoadMoreStatus.doingFirstLoadMore
            : SearchScreenLoadMoreStatus.doingLoadMore,
      ).dispatch(context);
    }

    late CameraListResponse response;

    // pmLog('The cameras search keyword is: "${widget.keyword}"');

    if (widget.keyword.isNotEmpty) {
      response = await _cameraListService.fetch(
        limit: _loadMorePerPage,
        lastId: isRefresh ? null : widget.searchData.loadMoreLastId,
        keyword: widget.keyword,
      );
    } else {
      response = await _cameraListService.fetch(
        limit: _loadMorePerPage,
        lastId: isRefresh ? null : widget.searchData.loadMoreLastId,
      );
    }

    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    _handleCameraListResponse(response, isRefresh);

    if (!isRefresh && mounted) {
      SearchLoadMoreNotification(
        cameraSearchScreen: SearchScreenLoadMoreStatus.idle,
      ).dispatch(context);
    }

    _isFetchingData = false;
  }

  void _handleCameraListResponse(CameraListResponse response, bool isRefresh) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    final searchCamerasReactiveService = ref.read(
      searchCamerasReactiveServiceProvider,
    );

    final bool isFirstLoad = _isFirstLoad();

    if (response.data.isEmpty) {
      if (isRefresh || isFirstLoad) {
        searchCamerasReactiveService.clear();
      }

      ref.read(searchCamerasDataProvider.notifier).setLoadMoreEndReached(true);
      return;
    }

    if (response.data.length < _loadMorePerPage) {
      ref.read(searchCamerasDataProvider.notifier).setLoadMoreEndReached(true);
    } else {
      ref.read(searchCamerasDataProvider.notifier).setLoadMoreEndReached(false);
    }

    ref
        .read(searchCamerasDataProvider.notifier)
        .setLastId(response.data.last.id);

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh || isFirstLoad) {
      searchCamerasReactiveService.replaceAll(response.data);
    } else {
      searchCamerasReactiveService.addItems(response.data);
    }
  }
}
