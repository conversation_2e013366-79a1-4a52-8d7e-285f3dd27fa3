import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/dto/artist_partial_data.dart';
import 'package:portraitmode/artist/http_responses/artist_response.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';
import 'package:portraitmode/artist/services/artist_service.dart';
import 'package:portraitmode/artist/widgets/artist_detail/artist_not_found.dart';
import 'package:portraitmode/artist/widgets/artist_detail/artist_profile_loading.dart';
import 'package:portraitmode/artist/widgets/artist_detail/blocked_profile.dart';
import 'package:portraitmode/artist/widgets/artist_detail/blocking_profile.dart';
import 'package:portraitmode/artist/widgets/artist_detail/my_profile.dart';
import 'package:portraitmode/artist/widgets/artist_detail/other_profile.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/category/http_responses/category_list_response.dart';
import 'package:portraitmode/category/providers/categories_provider.dart';
import 'package:portraitmode/category/services/category_list_service.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/max_width/max_width.dart';
import 'package:portraitmode/notification/utils/notification_util.dart';
import 'package:portraitmode/profile/providers/my_album_provider.dart';
import 'package:portraitmode/profile/providers/profile_provider.dart';

class ArtistDetailScreen extends ConsumerStatefulWidget {
  const ArtistDetailScreen({
    super.key,
    this.useBackButton = true,
    this.isOwnProfile,
    required this.partialData,
  });

  final bool useBackButton;
  final bool? isOwnProfile;
  final ArtistPartialData partialData;

  @override
  ArtistDetailScreenState createState() => ArtistDetailScreenState();
}

class ArtistDetailScreenState extends ConsumerState<ArtistDetailScreen> {
  final _artistService = ArtistService();
  final _categoryListService = CategoryListService();

  late bool _isOwnProfile;
  late int _profileId;

  final ValueNotifier<bool> _isLoadingNotifier = ValueNotifier(true);
  final ValueNotifier<ArtistData?> _loadedArtistNotifier = ValueNotifier(null);

  @override
  void initState() {
    _profileId = LocalUserService.userId ?? 0;

    if (widget.isOwnProfile != null) {
      _isOwnProfile = widget.isOwnProfile!;
    } else {
      _isOwnProfile = _profileId == widget.partialData.id;
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _getScreenData();
    });

    super.initState();
  }

  @override
  dispose() {
    _isLoadingNotifier.dispose();
    _loadedArtistNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: MaxWidthBuilder(
            maxWidth: 768.0,
            builder: (BuildContext ctx, BoxConstraints constraints) {
              final double containerWidth = constraints.maxWidth;

              return ValueListenableBuilder(
                valueListenable: _isLoadingNotifier,
                builder: (context, isLoading, child) {
                  if (isLoading) {
                    return ArtistProfileLoading(artist: widget.partialData);
                  }

                  return child!;
                },
                child: ValueListenableBuilder(
                  valueListenable: _loadedArtistNotifier,
                  builder: (context, artist, child) {
                    if (_isLoadingNotifier.value) {
                      return ArtistProfileLoading(artist: widget.partialData);
                    }

                    return _buildProfileWidget(
                      artist: artist,
                      containerWidth: containerWidth,
                    );
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildProfileWidget({
    required ArtistData? artist,
    required double containerWidth,
  }) {
    if (artist == null) {
      return const ArtistNotFound();
    }

    if (_isOwnProfile) {
      return MyProfile(
        useBackButton: widget.useBackButton,
        containerWidth: containerWidth,
        artist: artist,
      );
    }

    final artistProps = ref.watch(artistPropsProvider(artist.id));
    final isBlocked = artistProps.isBlocked == true;
    final isBlocking = artistProps.isBlocking == true;

    if (isBlocked) {
      return BlockedProfile(containerWidth: containerWidth, artist: artist);
    }

    if (isBlocking) {
      return BlockingProfile(containerWidth: containerWidth, artist: artist);
    }

    return OtherProfile(
      useBackButton: widget.useBackButton,
      containerWidth: containerWidth,
      artist: artist,
    );
  }

  Future<void> _getScreenData() async {
    final List<dynamic> responses = await Future.wait([
      (widget.partialData.id == 0
          ? _artistService.findByNicename(widget.partialData.nicename)
          : _artistService.find(widget.partialData.id)),
      _categoryListService.fetch(),
    ]);

    final ArtistResponse artistResponse = responses[0];

    // Check session ended only on 1 sample.
    if (!artistResponse.success) {
      if (authUtil.errorCodeRequiresLogin(artistResponse.errorCode)) {
        if (mounted) {
          showSessionEndedDialog(context, ref);
        }
      }
    }

    _isLoadingNotifier.value = false;

    if (artistResponse.success && artistResponse.data != null) {
      final ArtistData artist = artistResponse.data!;

      _loadedArtistNotifier.value = artist;

      ref.read(artistStoreProvider.notifier).updateItem(artist);

      if (_isOwnProfile) {
        ref.read(profileProvider.notifier).replaceFromArtistData(artist);
        ref.read(myAlbumProvider.notifier).replaceAll(artist.albums ?? []);

        // No need to await for this async call.
        LocalUserService.replaceFromArtistData(artist);
      }

      // No need to await for this async call.
      NotificationUtil().fetch(ref, artist.id);
    }

    final CategoryListResponse categoryListResponse = responses[1];

    if (categoryListResponse.success) {
      ref
          .read(categoriesReactiveServiceProvider)
          .replaceAll(categoryListResponse.data);
    }
  }
}
