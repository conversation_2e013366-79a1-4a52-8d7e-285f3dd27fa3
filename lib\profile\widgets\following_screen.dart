// following_screen.dart
// This screen displays artist feed

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/scroll_util.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/http_responses/artist_list_response.dart';
import 'package:portraitmode/artist/services/follow_list_service.dart';
import 'package:portraitmode/artist/widgets/artist_list_item.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/home/<USER>/empty_following_notice.dart';

class FollowingScreen extends ConsumerStatefulWidget {
  const FollowingScreen({super.key});

  @override
  FollowingScreenState createState() => FollowingScreenState();
}

class FollowingScreenState extends ConsumerState<FollowingScreen> {
  final _scrollController = ScrollController();
  final FollowListService _followListService = FollowListService();
  final int _loadMorePerPage = LoadMoreConfig.smallItemsPerPage;

  int? _loadMoreLastId;
  int? _loadMoreLastTotalPhotos;

  final _isFetchingNotifier = ValueNotifier(false);
  final _noMoreDataNotifier = ValueNotifier(false);
  final ValueNotifier<List<ArtistData>> _artistListNotifier = ValueNotifier([]);

  @override
  void initState() {
    super.initState();

    _scrollController.addListener(_onScroll);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _loadMore();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);

    _scrollController.dispose();
    _isFetchingNotifier.dispose();
    _noMoreDataNotifier.dispose();
    _artistListNotifier.dispose();

    super.dispose();
  }

  double _getLoadMoreTriggerPoint(double maxExtent) {
    // Calculate how much of the content should remain when triggering load more
    double remainingContent = maxExtent * LoadMoreConfig.tresholdPercentage;

    // Apply min/max constraints
    remainingContent = remainingContent.clamp(
      LoadMoreConfig.minTreshold,
      LoadMoreConfig.maxTreshold,
    );

    // Return the scroll position that should trigger load more
    return maxExtent - remainingContent;
  }

  bool _canLoadMore() {
    return !_isFetchingNotifier.value && !_noMoreDataNotifier.value;
  }

  void _onScroll() {
    // Safe some resource by early checking.
    if (!_canLoadMore()) return;

    double triggerPoint = _getLoadMoreTriggerPoint(
      _scrollController.position.maxScrollExtent,
    );

    // Handle load more when scrolling reaches the trigger point
    if (_scrollController.position.pixels >= triggerPoint) {
      if (_canLoadMore()) _loadMore();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PmAppBar(
        scrollController: _scrollController,
        titleText: "Following",
        automaticallyImplyLeading: true,
        useLogo: false,
        actions: const [],
      ),
      body: SafeArea(
        child: Container(
          constraints: BoxConstraints(maxWidth: ScreenStyleConfig.maxWidth),
          child: RefreshIndicator(
            color: context.colors.brandColor,
            elevation: 0.0,
            onRefresh: _handleRefresh,
            child: ValueListenableBuilder(
              valueListenable: _noMoreDataNotifier,
              builder: (context, noMoreData, child) {
                return ValueListenableBuilder(
                  valueListenable: _isFetchingNotifier,
                  builder: (context, isFetching, child) {
                    return ValueListenableBuilder(
                      valueListenable: _artistListNotifier,
                      builder: (context, artistList, child) {
                        if (!isFetching && noMoreData && artistList.isEmpty) {
                          return Center(child: const EmptyFollowingNotice());
                        }

                        return ListView.builder(
                          controller: _scrollController,
                          cacheExtent: getVerticalScrollCacheExtent(context),
                          itemCount:
                              artistList.length +
                              (isFetching && !noMoreData ? 1 : 0),
                          itemBuilder: (BuildContext context, int index) {
                            if (index == artistList.length && isFetching) {
                              return Padding(
                                padding: EdgeInsets.only(
                                  top: artistList.isEmpty ? 0 : 16.0,
                                ),
                                child: LinearProgressIndicator(
                                  color: context.colors.baseColorAlt,
                                ),
                              );
                            }

                            double marginTop = index == 0
                                ? LayoutConfig.contentTopGap
                                : 12.0;

                            return Padding(
                              padding: EdgeInsets.only(
                                left: ScreenStyleConfig.horizontalPadding,
                                right: ScreenStyleConfig.horizontalPadding,
                                top: marginTop,
                              ),
                              child: ArtistListItem(
                                index: index,
                                artist: artistList[index],
                                isFollowingScreen: true,
                              ),
                            );
                          },
                        );
                      },
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    _loadMoreLastId = null;
    _loadMoreLastTotalPhotos = null;

    _noMoreDataNotifier.value = false;

    _loadMore();
  }

  Future<void> _loadMore() async {
    _isFetchingNotifier.value = true;

    final ArtistListResponse response = await _followListService
        .fetchFollowings(
          limit: _loadMorePerPage,
          lastId: _loadMoreLastId,
          lastTotalPhotos: _loadMoreLastTotalPhotos,
        );

    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    _handleArtistListResponse(response);
    _isFetchingNotifier.value = false;
  }

  Future<void> _handleArtistListResponse(ArtistListResponse response) async {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    if (response.data.isEmpty) {
      _noMoreDataNotifier.value = true;
      return;
    }

    _loadMoreLastId = response.data.last.id;
    _loadMoreLastTotalPhotos = response.data.last.totalPhotos;

    _artistListNotifier.value = [
      ..._artistListNotifier.value,
      ...response.data,
    ];
  }
}
