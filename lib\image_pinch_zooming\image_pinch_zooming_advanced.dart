// image_pinch_zooming.dart

import 'dart:async';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A highly optimized widget for pinch-to-zoom interaction on images.
///
/// This version is using custom gesture recognizers.
class ImagePinchZooming extends StatefulWidget {
  const ImagePinchZooming({
    super.key,
    required this.image,
    this.zoomedBackgroundColor = Colors.black54,
    this.hideStatusBarWhileZooming = false,
    this.minScale = 1.0,
    this.maxScale = 4.0,
    this.animationDuration = const Duration(milliseconds: 200),
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.onTwoFingersOn,
    this.onTwoFingersOff,
    this.onZoomStart,
    this.onZoomEnd,
  });

  final Widget image;
  final Color zoomedBackgroundColor;
  final bool hideStatusBarWhileZooming;
  final double minScale;
  final double maxScale;
  final Duration animationDuration;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onTwoFingersOn;
  final VoidCallback? onTwoFingersOff;
  final VoidCallback? onZoomStart;
  final VoidCallback? onZoomEnd;

  @override
  State<ImagePinchZooming> createState() => _ImagePinchZoomingState();
}

class _ImagePinchZoomingState extends State<ImagePinchZooming>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  OverlayEntry? _overlayEntry;
  Offset? _initialFocalPoint;
  Offset? _widgetOrigin;
  Size? _widgetSize;
  bool _isZooming = false;
  bool _isReversing = false;
  int _activePointers = 0;

  // Store original system UI mode for proper restoration
  SystemUiMode? _originalSystemUiMode;

  // Use a single key for overlay management
  final GlobalKey<_PinchZoomOverlayState> _overlayKey = GlobalKey();

  // Track app lifecycle for system UI restoration
  bool _isAppInForeground = true;

  // Keep references to gesture recognizers for proper cleanup
  _CustomScaleGestureRecognizer? _scaleRecognizer;
  _CustomTapGestureRecognizer? _tapRecognizer;
  _CustomLongPressGestureRecognizer? _longPressRecognizer;

  @override
  void initState() {
    super.initState();

    // Add app lifecycle observer
    WidgetsBinding.instance.addObserver(this);

    // Store original system UI mode if we need to hide status bar
    if (widget.hideStatusBarWhileZooming) {
      _storeOriginalSystemUiMode();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _cleanupOverlay();
    _restoreSystemUI();

    // Properly dispose gesture recognizers
    _scaleRecognizer?.dispose();
    _tapRecognizer?.dispose();
    _longPressRecognizer?.dispose();

    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        _isAppInForeground = false;
        // Restore system UI when app goes to background
        if (_isZooming && widget.hideStatusBarWhileZooming) {
          _restoreSystemUI();
        }
        break;
      case AppLifecycleState.resumed:
        _isAppInForeground = true;
        // Re-hide system UI when app comes back to foreground
        if (_isZooming && widget.hideStatusBarWhileZooming) {
          _handleSystemUI();
        }
        break;
      case AppLifecycleState.detached:
        _isAppInForeground = false;
        break;
      case AppLifecycleState.hidden:
        _isAppInForeground = false;
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: RawGestureDetector(
        gestures: {
          _CustomScaleGestureRecognizer:
              GestureRecognizerFactoryWithHandlers<
                _CustomScaleGestureRecognizer
              >(
                () {
                  _scaleRecognizer?.dispose();
                  _scaleRecognizer = _CustomScaleGestureRecognizer();
                  return _scaleRecognizer!;
                },
                (_CustomScaleGestureRecognizer instance) {
                  instance.onStart = _handleScaleStart;
                  instance.onUpdate = _handleScaleUpdate;
                  instance.onEnd = _handleScaleEnd;
                },
              ),
          _CustomTapGestureRecognizer:
              GestureRecognizerFactoryWithHandlers<_CustomTapGestureRecognizer>(
                () {
                  _tapRecognizer?.dispose();
                  _tapRecognizer = _CustomTapGestureRecognizer();
                  return _tapRecognizer!;
                },
                (_CustomTapGestureRecognizer instance) {
                  instance.onTap = widget.onTap;
                  instance.onDoubleTap = widget.onDoubleTap;
                },
              ),
          _CustomLongPressGestureRecognizer:
              GestureRecognizerFactoryWithHandlers<
                _CustomLongPressGestureRecognizer
              >(
                () {
                  _longPressRecognizer?.dispose();
                  _longPressRecognizer = _CustomLongPressGestureRecognizer();
                  return _longPressRecognizer!;
                },
                (_CustomLongPressGestureRecognizer instance) {
                  instance.onLongPress = widget.onLongPress;
                },
              ),
        },
        child: Listener(
          onPointerDown: _handlePointerDown,
          onPointerUp: _handlePointerUp,
          onPointerCancel: _handlePointerCancel,
          child: Visibility(
            visible: !_isZooming,
            maintainSize: true,
            maintainAnimation: true,
            maintainState: true,
            child: widget.image,
          ),
        ),
      ),
    );
  }

  void _handlePointerDown(PointerDownEvent event) {
    _activePointers++;

    if (_activePointers >= 2) {
      // Only notify scale recognizer about multi-pointer mode
      // Don't disable tap recognizer immediately - let it handle its own logic
      widget.onTwoFingersOn?.call();
    }
  }

  void _handlePointerUp(PointerUpEvent event) {
    _activePointers = (_activePointers - 1).clamp(0, 10);

    if (_activePointers < 2) {
      // Exit multi-pointer mode
      widget.onTwoFingersOff?.call();

      // Fix: Trigger scale end when fingers are released during zooming
      if (_isZooming && !_isReversing) {
        _handleScaleEndFromPointer();
      }
    }
  }

  void _handlePointerCancel(PointerCancelEvent event) {
    _activePointers = 0;
    // Exit multi-pointer mode
    widget.onTwoFingersOff?.call();

    // Fix: Trigger scale end when gesture is cancelled during zooming
    if (_isZooming && !_isReversing) {
      _handleScaleEndFromPointer();
    }
  }

  void _handleScaleStart(ScaleStartDetails details) {
    if (_overlayEntry != null || _isReversing || _activePointers < 2) return;

    try {
      // Safely get widget bounds with null safety
      final renderBox = context.findRenderObject() as RenderBox?;
      if (renderBox?.hasSize != true || !renderBox!.attached) return;

      _widgetSize = renderBox.size;
      _widgetOrigin = renderBox.localToGlobal(Offset.zero);
      _initialFocalPoint = details.focalPoint;

      if (mounted) {
        setState(() {
          _isZooming = true;
        });
      }

      _handleSystemUI();
      widget.onZoomStart?.call();
      _showOverlay();
    } catch (e) {
      // Handle any coordinate calculation errors gracefully
      debugPrint('Error in _handleScaleStart: $e');
      return;
    }
  }

  void _handleScaleUpdate(ScaleUpdateDetails details) {
    if (_isReversing || _activePointers < 2) return;

    // Direct update for smooth real-time performance
    _performScaleUpdate(details);
  }

  void _performScaleUpdate(ScaleUpdateDetails details) {
    // Check if overlay and its state are available
    final overlayState = _overlayKey.currentState;
    if (overlayState == null ||
        _widgetOrigin == null ||
        _initialFocalPoint == null ||
        _widgetSize == null) {
      return;
    }

    try {
      final clampedScale = details.scale.clamp(
        widget.minScale,
        widget.maxScale,
      );

      // Simplified position calculation similar to backup implementation
      // This ensures the image follows finger movement correctly
      final newPosition =
          _widgetOrigin! - (_initialFocalPoint! - details.focalPoint);

      overlayState._updateTransform(newPosition, clampedScale);
    } catch (e) {
      debugPrint('Error in _performScaleUpdate: $e');
    }
  }

  void _handleScaleEnd(ScaleEndDetails details) async {
    if (_isReversing || !_isZooming) return;
    await _performScaleEnd();
  }

  // Fix: Add method to handle scale end from pointer events
  void _handleScaleEndFromPointer() async {
    if (_isReversing || !_isZooming) return;
    await _performScaleEnd();
  }

  // Fix: Extract common scale end logic to avoid duplication
  Future<void> _performScaleEnd() async {
    _isReversing = true;
    widget.onZoomEnd?.call();

    try {
      await _overlayKey.currentState?._animateToOriginal();
    } catch (e) {
      debugPrint('Error in scale end animation: $e');
    }

    _cleanupOverlay();
    _restoreSystemUI();

    if (mounted) {
      setState(() {
        _isZooming = false;
        _isReversing = false;
      });
    }
  }

  void _showOverlay() {
    if (_overlayEntry != null || _widgetSize == null || _widgetOrigin == null) {
      return;
    }

    try {
      final overlay = Overlay.of(context);
      _overlayEntry = OverlayEntry(
        builder: (context) => _PinchZoomOverlay(
          key: _overlayKey,
          image: widget.image,
          originalSize: _widgetSize!,
          originalPosition: _widgetOrigin!,
          backgroundColor: widget.zoomedBackgroundColor,
          animationDuration: widget.animationDuration,
        ),
      );

      overlay.insert(_overlayEntry!);
    } catch (e) {
      debugPrint('Error showing overlay: $e');
      _overlayEntry = null;
    }
  }

  void _cleanupOverlay() {
    try {
      _overlayEntry?.remove();
    } catch (e) {
      debugPrint('Error removing overlay: $e');
    }

    _overlayEntry = null;
    _widgetOrigin = null;
    _widgetSize = null;
    _initialFocalPoint = null;
  }

  void _storeOriginalSystemUiMode() {
    // Note: There's no direct way to query current SystemUiMode in Flutter
    // We'll assume the most common default mode
    _originalSystemUiMode = SystemUiMode.edgeToEdge;
  }

  void _handleSystemUI() {
    if (!widget.hideStatusBarWhileZooming || !_isAppInForeground) return;

    try {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
    } catch (e) {
      debugPrint('Error setting system UI mode: $e');
    }
  }

  void _restoreSystemUI() {
    if (!widget.hideStatusBarWhileZooming) return;

    try {
      // Restore to original mode if stored, otherwise use sensible default
      SystemChrome.setEnabledSystemUIMode(
        _originalSystemUiMode ?? SystemUiMode.edgeToEdge,
      );
    } catch (e) {
      debugPrint('Error restoring system UI mode: $e');
    }
  }
}

// FIXED: Custom gesture recognizers with proper priority handling
class _CustomScaleGestureRecognizer extends ScaleGestureRecognizer {
  @override
  void rejectGesture(int pointer) {
    // FIXED: Only accept pinch gestures (2+ fingers), reject single finger gestures
    // This allows tap gestures to work properly
    if (pointerCount < 2) {
      super.rejectGesture(pointer);
    } else {
      acceptGesture(pointer);
    }
  }

  @override
  void addAllowedPointer(PointerDownEvent event) {
    // FIXED: Only start tracking when we potentially have a multi-finger gesture
    super.addAllowedPointer(event);
  }

  @override
  void didStopTrackingLastPointer(int pointer) {
    // Fix: Ensure scale end is called when last pointer is lifted
    super.didStopTrackingLastPointer(pointer);
  }
}

class _CustomTapGestureRecognizer extends TapGestureRecognizer {
  VoidCallback? onDoubleTap;

  // Track consecutive taps for double tap detection
  int _tapCount = 0;
  Timer? _tapTimer;

  // Store the last tap position for double tap validation
  Offset? _lastTapPosition;

  // Configuration for double tap detection
  static const Duration _doubleTapTimeout = Duration(milliseconds: 150);
  static const double _doubleTapSlop = 50.0; // Maximum distance between taps

  @override
  void addAllowedPointer(PointerDownEvent event) {
    // FIXED: Always allow single finger tap gestures
    // This ensures tap gestures are properly recognized
    super.addAllowedPointer(event);
  }

  @override
  void rejectGesture(int pointer) {
    // Reset tap sequence when gesture is rejected
    _resetTapSequence();
    super.rejectGesture(pointer);
  }

  @override
  void acceptGesture(int pointer) {
    // Proceed with tap processing when gesture is accepted
    super.acceptGesture(pointer);
  }

  @override
  void handleTapDown({required PointerDownEvent down}) {
    // Handle tap down normally
    super.handleTapDown(down: down);
  }

  @override
  void handleTapUp({
    required PointerDownEvent down,
    required PointerUpEvent up,
  }) {
    // Handle tap sequence properly for double tap detection
    _handleTapSequence(down.localPosition);
    // super.handleTapUp(down: down, up: up);
  }

  @override
  void handleTapCancel({
    required PointerDownEvent down,
    PointerCancelEvent? cancel,
    required String reason,
  }) {
    _resetTapSequence();
    super.handleTapCancel(down: down, cancel: cancel, reason: reason);
  }

  void _handleTapSequence(Offset position) {
    _tapCount++;

    // Check if this could be a double tap
    if (_tapCount == 1) {
      _lastTapPosition = position;
      _tapTimer?.cancel();
      _tapTimer = Timer(_doubleTapTimeout, () {
        // Single tap confirmed - call the single tap callback
        if (onTap != null) {
          onTap!();
        }
        _resetTapSequence();
      });
    } else if (_tapCount == 2) {
      // Check if positions are close enough for double tap
      if (_lastTapPosition != null &&
          (position - _lastTapPosition!).distance <= _doubleTapSlop) {
        // Double tap confirmed - cancel single tap timer and call double tap
        _tapTimer?.cancel();
        _resetTapSequence();
        onDoubleTap?.call();
      } else {
        // Positions too far apart, treat as separate single taps
        _resetTapSequence();
        _lastTapPosition = position;
        _tapCount = 1;
        _tapTimer = Timer(_doubleTapTimeout, () {
          if (onTap != null) {
            onTap!();
          }
          _resetTapSequence();
        });
      }
    } else {
      // More than 2 taps, reset and treat as single tap
      _resetTapSequence();
      if (onTap != null) {
        onTap!();
      }
    }
  }

  void _resetTapSequence() {
    _tapCount = 0;
    _lastTapPosition = null;
    _tapTimer?.cancel();
    _tapTimer = null;
  }

  @override
  void dispose() {
    // Ensure clean disposal
    _resetTapSequence();
    super.dispose();
  }
}

class _CustomLongPressGestureRecognizer extends LongPressGestureRecognizer {
  @override
  void addAllowedPointer(PointerDownEvent event) {
    // FIXED: Always allow long press gestures to be considered
    // The competition will be resolved by the gesture arena
    super.addAllowedPointer(event);
  }

  @override
  void dispose() {
    // Ensure clean disposal
    super.dispose();
  }
}

// Overlay widget for zoom effect
class _PinchZoomOverlay extends StatefulWidget {
  final Widget image;
  final Size originalSize;
  final Offset originalPosition;
  final Color backgroundColor;
  final Duration animationDuration;

  const _PinchZoomOverlay({
    super.key,
    required this.image,
    required this.originalSize,
    required this.originalPosition,
    required this.backgroundColor,
    required this.animationDuration,
  });

  @override
  State<_PinchZoomOverlay> createState() => _PinchZoomOverlayState();
}

class _PinchZoomOverlayState extends State<_PinchZoomOverlay>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  // Use ValueNotifiers for better performance
  late ValueNotifier<Offset> _positionNotifier;
  late ValueNotifier<double> _scaleNotifier;
  bool _isAnimating = false;

  // Cache screen size for performance
  Size? _cachedScreenSize;

  @override
  void initState() {
    super.initState();
    _positionNotifier = ValueNotifier<Offset>(widget.originalPosition);
    _scaleNotifier = ValueNotifier<double>(1.0);
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
  }

  @override
  void dispose() {
    if (_animationController.isAnimating) {
      _animationController.stop();
    }
    _animationController.dispose();
    _positionNotifier.dispose();
    _scaleNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Cache screen size on first build
    _cachedScreenSize ??= MediaQuery.sizeOf(context);

    // Use ValueListenableBuilder for both animation and real-time updates
    return ValueListenableBuilder<Offset>(
      valueListenable: _positionNotifier,
      builder: (context, position, child) {
        return ValueListenableBuilder<double>(
          valueListenable: _scaleNotifier,
          builder: (context, scale, child) {
            return _buildOverlayContent(position, scale, _getOpacity(scale));
          },
        );
      },
    );
  }

  Widget _buildOverlayContent(Offset position, double scale, double opacity) {
    return RepaintBoundary(
      child: Stack(
        children: [
          // Background with direct opacity for better performance
          if (opacity > 0.0)
            Container(
              width: double.infinity,
              height: double.infinity,
              color: widget.backgroundColor.withValues(alpha: opacity),
            ),
          // Zoomed image with simplified positioning similar to backup
          Positioned(
            top: position.dy,
            left: position.dx,
            width: widget.originalSize.width,
            height: widget.originalSize.height,
            child: RepaintBoundary(
              child: Transform.scale(scale: scale, child: widget.image),
            ),
          ),
        ],
      ),
    );
  }

  double _getOpacity(double scale) {
    // Simplified opacity calculation similar to backup implementation
    if (scale <= 1.0) return 0.0;
    if (_cachedScreenSize == null) return 0.0;

    final maxScale = _cachedScreenSize!.height / widget.originalSize.height;
    if (maxScale <= 1.0) return 1.0;

    return ((scale - 1.0) / (maxScale - 1.0)).clamp(0.0, 1.0);
  }

  void _updateTransform(Offset position, double scale) {
    if (_isAnimating || !mounted) return;

    // Update ValueNotifiers for efficient rebuilds
    if (_positionNotifier.value != position) {
      _positionNotifier.value = position;
    }
    if (_scaleNotifier.value != scale) {
      _scaleNotifier.value = scale;
    }
  }

  Future<void> _animateToOriginal() async {
    if (_isAnimating || !mounted) return;

    _isAnimating = true;

    try {
      final currentScale = _scaleNotifier.value;
      final currentPosition = _positionNotifier.value;

      // Reset and configure the existing animation controller for smoother animation
      _animationController.reset();
      _animationController.duration = widget.animationDuration;

      // Add listener for smooth interpolation
      void animationListener() {
        if (!mounted) return;

        final progress = Curves.fastOutSlowIn.transform(
          _animationController.value,
        );

        // Interpolate scale
        final newScale = currentScale + (1.0 - currentScale) * progress;

        // Interpolate position
        final newPosition = Offset.lerp(
          currentPosition,
          widget.originalPosition,
          progress,
        )!;

        // Update notifiers directly for immediate effect
        _scaleNotifier.value = newScale;
        _positionNotifier.value = newPosition;
      }

      _animationController.addListener(animationListener);

      // Run the animation
      await _animationController.forward(from: 0.0);

      // Clean up the listener
      _animationController.removeListener(animationListener);

      if (mounted) {
        _animationController.reset();
      }
    } catch (e) {
      debugPrint('Error in animation: $e');
    } finally {
      _isAnimating = false;
    }
  }
}
