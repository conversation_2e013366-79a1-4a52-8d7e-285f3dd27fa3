import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/log_util.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/appbar/title_with_logo.dart';
import 'package:portraitmode/home/<USER>/following_photos_tab_content.dart';
import 'package:portraitmode/home/<USER>/home_tabbar_delegate.dart';
import 'package:portraitmode/home/<USER>/latest_photos_tab_content.dart';
import 'package:portraitmode/photo/widgets/photo_detail/profile_avatar.dart';

enum SwipeDirection { left, right, none }

class HomeScreen extends ConsumerStatefulWidget {
  final int? initialIndex;
  final ValueNotifier<VoidCallback?> refreshNotifier;

  const HomeScreen({
    super.key,
    this.initialIndex,
    required this.refreshNotifier,
  });

  @override
  HomeScreenState createState() => HomeScreenState();
}

class HomeScreenState extends ConsumerState<HomeScreen>
    with TickerProviderStateMixin {
  TabController? _tabController;

  final ValueNotifier<SwipeDirection> _swipeDirectionNotifier = ValueNotifier(
    SwipeDirection.none,
  );

  double? _previousAnimationValue;
  final double _dividerBarWidth = 2.0;

  final ValueNotifier<VoidCallback?> _latestPhotosRefreshNotifier =
      ValueNotifier(null);

  final ValueNotifier<VoidCallback?> _followingPhotosRefreshNotifier =
      ValueNotifier(null);

  final ValueNotifier<bool> _blockScrollNotifier = ValueNotifier(false);

  @override
  void initState() {
    super.initState();

    widget.refreshNotifier.value = _scrollToTop;

    _tabController = TabController(
      length: 2,
      vsync: this,
      initialIndex: widget.initialIndex ?? 0,
    );

    _tabController?.animation?.addListener(_handleTabSwipe);
  }

  @override
  void dispose() {
    _tabController?.animation?.removeListener(_handleTabSwipe);

    _tabController?.dispose();
    _latestPhotosRefreshNotifier.dispose();
    _followingPhotosRefreshNotifier.dispose();
    _blockScrollNotifier.dispose();

    super.dispose();
  }

  void _handleTabSwipe() {
    if (_previousAnimationValue == null) {
      _previousAnimationValue = _tabController?.animation?.value;
      return;
    }

    final double? tabAnimValue = _tabController?.animation?.value;
    if (tabAnimValue == null) return;

    if (tabAnimValue % 1 == 0) {
      // pmLog("Swipe completed");
      _swipeDirectionNotifier.value = SwipeDirection.none;
    } else {
      SwipeDirection direction = SwipeDirection.none;

      if (tabAnimValue > _previousAnimationValue!) {
        // Handle right swipe
        // pmLog("Swiping to the right");
        direction = SwipeDirection.right;
      } else if (tabAnimValue < _previousAnimationValue!) {
        // Handle left swipe
        // pmLog("Swiping to the left");
        direction = SwipeDirection.left;
      }

      _swipeDirectionNotifier.value = direction;
    }

    _previousAnimationValue = tabAnimValue;
  }

  @override
  Widget build(BuildContext context) {
    pmLog('🖥️ Building HomeScreen');

    return Scaffold(
      primary: false,
      body: SafeArea(
        child: ValueListenableBuilder(
          valueListenable: _blockScrollNotifier,
          builder: (context, blockScrolling, child) {
            return NestedScrollView(
              physics: blockScrolling
                  ? const NeverScrollableScrollPhysics()
                  : null,
              headerSliverBuilder:
                  (BuildContext context, bool innerBoxIsScrolled) {
                    return [
                      PmSliverAppBar(
                        primary: true,
                        automaticallyImplyLeading: false,
                        floating: false,
                        pinned: true,
                        title: SizedBox(
                          width: double.infinity,
                          child: TitleWithLogo(),
                        ),
                        actions: [
                          Padding(
                            padding: const EdgeInsets.only(
                              right: ScreenStyleConfig.horizontalPadding,
                            ),
                            child: ProfileAvatar(
                              size: 32.0,
                              toProfileScreen: true,
                            ),
                          ),
                        ],
                        bottom: PreferredSize(
                          preferredSize: Size.fromHeight(
                            AppConfig.appBarBorderWidth,
                          ),
                          child: Container(
                            height: AppConfig.appBarBorderWidth,
                            color: context.colors.borderColor,
                          ),
                        ),
                      ),
                      SliverPersistentHeader(
                        floating: false,
                        pinned: true,
                        delegate: HomeTabbarDelegate(
                          controller: _tabController,
                        ),
                      ),
                    ];
                  },
              body: TabBarView(
                controller: _tabController,
                physics: blockScrolling
                    ? const NeverScrollableScrollPhysics()
                    : null,
                children: [
                  LatestPhotosTabContent(
                    refreshNotifier: _latestPhotosRefreshNotifier,
                    onPhotoTwoFingersOn: () {
                      _blockScrollNotifier.value = true;
                    },
                    onPhotoTwoFingersOff: () {
                      _blockScrollNotifier.value = false;
                    },
                  ),
                  Stack(
                    children: [
                      FollowingPhotosTabContent(
                        refreshNotifier: _followingPhotosRefreshNotifier,
                        onPhotoTwoFingersOn: () {
                          _blockScrollNotifier.value = true;
                        },
                        onPhotoTwoFingersOff: () {
                          _blockScrollNotifier.value = false;
                        },
                      ),
                      Positioned(
                        top: 0.0,
                        left: 0.0,
                        child: ValueListenableBuilder(
                          valueListenable: _swipeDirectionNotifier,
                          builder: (context, swipeDirection, child) {
                            if (swipeDirection != SwipeDirection.none) {
                              return Container(
                                width: _dividerBarWidth,
                                height: MediaQuery.sizeOf(context).height,
                                color: context.colors.borderColor,
                              );
                            }

                            return const SizedBox.shrink();
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Future<void> _scrollToTop() async {
    if (_tabController?.index == 0) {
      _latestPhotosRefreshNotifier.value?.call();
      return;
    }

    if (_tabController?.index == 1) {
      _followingPhotosRefreshNotifier.value?.call();
      return;
    }
  }
}
