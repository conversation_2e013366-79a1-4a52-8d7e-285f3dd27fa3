// latest_photo_list_screen.dart
// This screen displays photo feed

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/dto/initial_data.dart';
import 'package:portraitmode/app/http_responses/initial_data_response.dart';
import 'package:portraitmode/app/providers/app_state_provider.dart';
import 'package:portraitmode/app/services/app_service.dart';
import 'package:portraitmode/app/utils/log_util.dart';
import 'package:portraitmode/app/utils/scroll_util.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';
import 'package:portraitmode/artist/dto/simple_artist_data.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/category/http_responses/category_list_response.dart';
import 'package:portraitmode/category/services/category_list_service.dart';
import 'package:portraitmode/category/widgets/category_list_slider.dart';
import 'package:portraitmode/common/utils/common_refresh_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/feedback_token/providers/feedback_token_amount_provider.dart';
import 'package:portraitmode/hive/dto/local_user_data.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/notification/providers/notification_provider.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/providers/latest_photos_provider.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/widgets/photo_list_item.dart';
import 'package:portraitmode/photo/widgets/trending_photo_list_slider.dart';

class LatestPhotoListScreen extends ConsumerStatefulWidget {
  final ValueNotifier<VoidCallback?>? refreshNotifier;

  const LatestPhotoListScreen({super.key, this.refreshNotifier});

  @override
  LatestPhotoListScreenState createState() => LatestPhotoListScreenState();
}

class LatestPhotoListScreenState extends ConsumerState<LatestPhotoListScreen> {
  final _scrollController = ScrollController();
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();

  final PhotoListService _photoListService = PhotoListService();
  final AppService _appService = AppService();
  final CategoryListService _categoryListService = CategoryListService();

  late final int _profileId;

  final int _loadMorePerPage = LoadMoreConfig.itemsPerPage;
  int _loadMoreLastId = 0;
  int _currentPageNumber = 0;
  bool _loadMoreEndReached = false;

  final _isFetchingNotifier = ValueNotifier(false);
  final ValueNotifier<List<PhotoData>> _trendingPhotoListNotifier =
      ValueNotifier([]);
  final ValueNotifier<bool> _blockScrollNotifier = ValueNotifier(false);

  bool _initialDataFetched = false;
  List<CategoryData> _unfollowedCategoryList = [];

  @override
  void initState() {
    super.initState();

    if (widget.refreshNotifier != null) {
      widget.refreshNotifier!.value = _scrollToTop;
    }

    _profileId = LocalUserService.userId ?? 0;
    _scrollController.addListener(_onScroll);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _triggerLoadMore();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);

    _scrollController.dispose();
    _isFetchingNotifier.dispose();
    _trendingPhotoListNotifier.dispose();
    _blockScrollNotifier.dispose();

    super.dispose();
  }

  double _getLoadMoreTriggerPoint(double maxExtent) {
    // Calculate how much of the content should remain when triggering load more
    double remainingContent = maxExtent * LoadMoreConfig.tresholdPercentage;

    // Apply min/max constraints
    remainingContent = remainingContent.clamp(
      LoadMoreConfig.minTreshold,
      LoadMoreConfig.maxTreshold,
    );

    // Return the scroll position that should trigger load more
    return maxExtent - remainingContent;
  }

  bool _canLoadMore() {
    return !_isFetchingNotifier.value && !_loadMoreEndReached;
  }

  void _onScroll() {
    // Safe some resource by early checking.
    if (!_canLoadMore()) return;

    double triggerPoint = _getLoadMoreTriggerPoint(
      _scrollController.position.maxScrollExtent,
    );

    // Handle load more when scrolling reaches the trigger point
    if (_scrollController.position.pixels >= triggerPoint) {
      if (_canLoadMore()) _triggerLoadMore();
    }
  }

  void _triggerLoadMore() async {
    _isFetchingNotifier.value = true;
    await _loadMore();
    _isFetchingNotifier.value = false;
  }

  @override
  Widget build(BuildContext context) {
    final bool behaveNewlyOpened = ref.watch(
      appStateProvider.select((data) => data.behaveNewlyOpened),
    );

    if (behaveNewlyOpened) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToTop();

        if (mounted) {
          // Reset the behaveNewlyOpened flag after handling it.
          ref.read(appStateProvider.notifier).setBehaveNewlyOpened(false);
        }
      });
    }

    final List<PhotoData> photoList = ref.watch(latestPhotosProvider);

    pmLog('🛠️ Building LatestPhotoListScreen screen');

    return Scaffold(
      appBar: PmAppBar(scrollController: _scrollController),
      body: SafeArea(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 768.0),
          child: RefreshIndicator(
            key: _refreshIndicatorKey,
            color: context.colors.brandColor,
            elevation: 0.0,
            onRefresh: _handleRefresh,
            child: ValueListenableBuilder(
              valueListenable: _isFetchingNotifier,
              builder: (context, isFetching, child) {
                return ValueListenableBuilder(
                  valueListenable: _blockScrollNotifier,
                  builder: (context, blockScrolling, child) {
                    return ListView.builder(
                      controller: _scrollController,
                      physics: blockScrolling
                          ? const NeverScrollableScrollPhysics()
                          : null,
                      cacheExtent: getVerticalScrollCacheExtent(context),
                      itemCount:
                          photoList.length +
                          (isFetching && !_loadMoreEndReached ? 1 : 0),
                      itemBuilder: (BuildContext context, int index) {
                        if (index == photoList.length && isFetching) {
                          return Padding(
                            padding: EdgeInsets.only(
                              top: photoList.isEmpty ? 0 : 16.0,
                            ),
                            child: LinearProgressIndicator(
                              color: context.colors.baseColorAlt,
                            ),
                          );
                        }

                        double marginTop = index == 0
                            ? LayoutConfig.contentTopGap
                            : 12.0;

                        return (index == 9 || index == 18
                            ? Column(
                                key: ValueKey(photoList[index].id),
                                children: [
                                  Padding(
                                    padding: EdgeInsets.only(
                                      top: marginTop,
                                      bottom: 20.0,
                                    ),
                                    child: (index == 9
                                        ? _buildCategoriesSlider()
                                        : ValueListenableBuilder(
                                            valueListenable:
                                                _trendingPhotoListNotifier,
                                            builder:
                                                (
                                                  context,
                                                  trendingPhotoList,
                                                  child,
                                                ) {
                                                  return TrendingPhotoListSlider(
                                                    photoList:
                                                        trendingPhotoList,
                                                  );
                                                },
                                          )),
                                  ),
                                  PhotoListItem(
                                    photo: photoList[index],
                                    isOwnProfile:
                                        photoList[index].authorId == _profileId,
                                    screenName: 'explore_screen',
                                    onTwoFingersOn: () {
                                      _blockScrollNotifier.value = true;
                                    },
                                    onTwoFingersOff: () {
                                      _blockScrollNotifier.value = false;
                                    },
                                  ),
                                ],
                              )
                            : PhotoListItem(
                                key: ValueKey(photoList[index].id),
                                photo: photoList[index],
                                isOwnProfile:
                                    photoList[index].authorId == _profileId,
                                margin: EdgeInsets.only(top: marginTop),
                                screenName: 'explore_screen',
                                onTwoFingersOn: () {
                                  _blockScrollNotifier.value = true;
                                },
                                onTwoFingersOff: () {
                                  _blockScrollNotifier.value = false;
                                },
                              ));
                      },
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCategoriesSlider() {
    return CategoryListSlider(
      categoryList: _unfollowedCategoryList,
      padding: const EdgeInsets.only(top: 20.0, bottom: 22.0),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: context.colors.borderColor, width: 1.0),
          bottom: BorderSide(color: context.colors.borderColor, width: 1.0),
        ),
      ),
    );
  }

  void _scrollToTop() async {
    scrollListToTop(_scrollController, _refreshIndicatorKey);
  }

  Future<void> _handleRefresh() async {
    _loadMoreEndReached = false;
    _loadMoreLastId = 0;

    _isFetchingNotifier.value = true;

    List<dynamic> reponses = await Future.wait([
      _photoListService.fetch(limit: _loadMorePerPage, lastId: _loadMoreLastId),
      _categoryListService.fetchUnfollowed(limit: 24),
      CommonRefreshUtil().fetch(ref, _profileId),
    ]);

    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    final PhotoListResponse photoListResponse = reponses[0];
    final CategoryListResponse categoryListResponse = reponses[1];

    if (categoryListResponse.success) {
      _unfollowedCategoryList = categoryListResponse.data;
    }

    _handlePhotoListResponse(photoListResponse, true);
    _isFetchingNotifier.value = false;
  }

  Future<void> _loadMore() async {
    final bool shouldUpdateTrendingPhotos = _currentPageNumber == 1;
    List<dynamic> responses = [];

    late PhotoListResponse photoListResponse;
    late PhotoListResponse trendingPhotoListResponse;

    if (_currentPageNumber == 1) {
      responses = await Future.wait([
        _photoListService.fetch(
          limit: _loadMorePerPage,
          lastId: _loadMoreLastId,
        ),
        _photoListService.fetchTrending(limit: 24),
      ]);

      photoListResponse = responses[0];
      trendingPhotoListResponse = responses[1];
    } else {
      if (_currentPageNumber == 0 && !_initialDataFetched) {
        late InitialDataResponse initialDataResponse;

        responses = await Future.wait([
          _photoListService.fetch(
            limit: _loadMorePerPage,
            lastId: _loadMoreLastId,
          ),
          _appService.fetchInitialData(),
        ]);

        photoListResponse = responses[0];
        initialDataResponse = responses[1];

        if (initialDataResponse.success && initialDataResponse.data != null) {
          final InitialData initialData = initialDataResponse.data!;

          if (initialData.simpleArtistData != null) {
            SimpleArtistData simpleArtistData = initialData.simpleArtistData!;

            await LocalUserService.replace(
              LocalUserData(
                userId: simpleArtistData.id,
                nicename: simpleArtistData.nicename,
                role: simpleArtistData.role,
                displayName: simpleArtistData.displayName,
                profileUrl: simpleArtistData.profileUrl,
                avatarUrl: simpleArtistData.avatarUrl,
                membershipType: simpleArtistData.membershipType,
              ),
            );
          }

          _unfollowedCategoryList = initialData.unfollowedCategoryList;

          if (initialData.notificationFetchResult != null) {
            ref
                .read(notificationProvider.notifier)
                .replace(initialData.notificationFetchResult!);
          }

          globalFeedbackTokensAmount = initialData.feedbackTokensAmount;

          _initialDataFetched = true;
        }
      } else {
        photoListResponse = await _photoListService.fetch(
          limit: _loadMorePerPage,
          lastId: _loadMoreLastId,
        );
      }
    }

    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    _handlePhotoListResponse(photoListResponse, false);

    if (shouldUpdateTrendingPhotos &&
        trendingPhotoListResponse.success &&
        trendingPhotoListResponse.data.isNotEmpty) {
      _trendingPhotoListNotifier.value = trendingPhotoListResponse.data;
    }
  }

  void _handlePhotoListResponse(PhotoListResponse response, bool isRefresh) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    final latestPhotosReactiveService = ref.read(
      latestPhotosReactiveServiceProvider,
    );

    final bool isFirstLoad = _loadMoreLastId == 0 || _loadMoreLastId == -1;

    if (response.data.isEmpty) {
      _loadMoreEndReached = true;

      if (isRefresh || isFirstLoad) {
        latestPhotosReactiveService.clear();
      }

      return;
    }

    if (response.data.length < _loadMorePerPage) {
      _loadMoreEndReached = true;
    }

    _loadMoreLastId = response.data.last.id;

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh || isFirstLoad) {
      _currentPageNumber = 1;
      latestPhotosReactiveService.replaceAll(response.data);
    } else {
      _currentPageNumber++;
      latestPhotosReactiveService.addItems(response.data);
    }
  }
}
