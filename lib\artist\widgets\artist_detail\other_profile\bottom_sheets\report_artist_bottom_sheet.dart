import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/artist/services/artist_service.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/base/base_response.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/form/submit_button.dart';
import 'package:portraitmode/modals/modal_drag_handle.dart';

class ReportArtistBottomSheet extends ConsumerStatefulWidget {
  final int artistId;

  const ReportArtistBottomSheet({super.key, required this.artistId});

  @override
  ReportArtistModalState createState() => ReportArtistModalState();
}

class ReportArtistModalState extends ConsumerState<ReportArtistBottomSheet> {
  final _artistService = ArtistService();
  bool _isLoading = false;
  String _selectedReason = '';

  final Map<String, String> _reasons = {
    'spam': 'Spam',
    'hate_speech': 'Hate speech',
    'harassment': 'Harassment',
    'other': 'Other',
  };

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      maxChildSize: BottomSheetConfig.maxChildSize,
      initialChildSize: 0.45,
      expand: false,
      builder: (context, scrollController) {
        return SafeArea(
          child: Column(
            children: [
              Expanded(
                child: ListView(
                  controller: scrollController,
                  children: [
                    const ModalDragHandle(),
                    const SizedBox(height: 8),
                    for (String reason in _reasons.keys)
                      ListTile(
                        title: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _reasons[reason]!,
                              style: const TextStyle(fontSize: 14.5),
                            ),
                            if (_selectedReason == reason)
                              const SizedBox(width: 5.0),
                            if (_selectedReason == reason)
                              Icon(
                                Ionicons.checkmark_circle_sharp,
                                color: context.colors.accentColor,
                                size: 15,
                              ),
                          ],
                        ),
                        onTap: () => _handleOnTap(reason),
                        // dense: true,
                        visualDensity: const VisualDensity(vertical: -3.0),
                      ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(
                  top: 8.0,
                  bottom: 14.0,
                  left: ScreenStyleConfig.horizontalPadding,
                  right: ScreenStyleConfig.horizontalPadding,
                ),
                child: SubmitButton(
                  buttonText: "Report",
                  width: double.infinity,
                  height: 40.0,
                  fontWeight: FontWeight.w600,
                  onPressed: () async => await _handleOnSubmit(),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _handleOnTap(String targettedAlbumSlug) async {
    if (!mounted) return;

    setState(() {
      if (_selectedReason == targettedAlbumSlug) {
        _selectedReason = '';
      } else {
        _selectedReason = targettedAlbumSlug;
      }
    });
  }

  Future<void> _handleOnSubmit() async {
    if (_isLoading) return;

    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    BaseResponse response = await _artistService.reportArtist(
      artistId: widget.artistId,
      reason: _selectedReason,
    );

    if (!response.success) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }
      }

      return;
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      Navigator.of(context).pop();
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(response.message)));
    }
  }
}
