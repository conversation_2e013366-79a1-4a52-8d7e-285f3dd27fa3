// categories_tab_content.dart
// This screen displays category feed

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/scroll_util.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/category/http_responses/category_list_response.dart';
import 'package:portraitmode/category/services/category_list_service.dart';
import 'package:portraitmode/category/widgets/category_list_item.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/search/dto/search_data.dart';
import 'package:portraitmode/search/misc.dart';
import 'package:portraitmode/search/providers/search_categories_data_provider.dart';
import 'package:portraitmode/search/providers/search_categories_provider.dart';

class CategoriesTabContent extends ConsumerStatefulWidget {
  const CategoriesTabContent({
    super.key,
    required this.searchData,
    required this.keyword,
    required this.dataList,
  });

  final CategoriesSearchData searchData;
  final String keyword;
  final List<CategoryData> dataList;

  @override
  CategoriesTabContentState createState() => CategoriesTabContentState();
}

class CategoriesTabContentState extends ConsumerState<CategoriesTabContent> {
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  final _categoryListService = CategoryListService();

  final int _loadMorePerPage = LoadMoreConfig.gridItemsPerPage;
  bool _isFetchingData = false;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _loadMore(false);
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  double _getLoadMoreTriggerPoint(double maxExtent) {
    // Calculate how much of the content should remain when triggering load more
    double remainingContent = maxExtent * LoadMoreConfig.tresholdPercentage;

    // Apply min/max constraints
    remainingContent = remainingContent.clamp(
      LoadMoreConfig.minTreshold,
      LoadMoreConfig.maxTreshold,
    );

    // Return the scroll position that should trigger load more
    return maxExtent - remainingContent;
  }

  bool _canLoadMore() {
    return !_isFetchingData && !widget.searchData.loadMoreEndReached;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: const BoxConstraints(maxWidth: 768.0),
      child: RefreshIndicator(
        key: _refreshIndicatorKey,
        color: context.colors.brandColor,
        elevation: 0.0,
        onRefresh: _handleRefresh,
        child: NotificationListener<ScrollNotification>(
          onNotification: (ScrollNotification scrollInfo) {
            final double triggerPoint = _getLoadMoreTriggerPoint(
              scrollInfo.metrics.maxScrollExtent,
            );

            // Handle load more when scrolling reaches the trigger point
            if (scrollInfo.metrics.pixels >= triggerPoint) {
              if (_canLoadMore()) _loadMore(false);
            }

            return false;
          },
          child: GridView.builder(
            // Remove the controller - let NestedScrollView handle scrolling
            padding: const EdgeInsets.all(8.0),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisSpacing: 8.0,
              crossAxisSpacing: 8.0,
              childAspectRatio: 1.8,
            ),
            cacheExtent: getVerticalScrollCacheExtent(context),
            itemCount: widget.dataList.length,
            itemBuilder: (BuildContext context, int index) {
              return CategoryListItem(
                key: ValueKey(widget.dataList[index].id),
                category: widget.dataList[index],
              );
            },
          ),
        ),
      ),
    );
  }

  bool _isFirstLoad() {
    return widget.searchData.offset == -1 || widget.searchData.offset == 0;
  }

  Future<void> _handleRefresh() async {
    _loadMore(true);
  }

  Future<void> _loadMore(bool isRefresh) async {
    _isFetchingData = true;

    if (!isRefresh && mounted) {
      SearchLoadMoreNotification(
        categorySearchScreen: _isFirstLoad()
            ? SearchScreenLoadMoreStatus.doingFirstLoadMore
            : SearchScreenLoadMoreStatus.doingLoadMore,
      ).dispatch(context);
    }

    late CategoryListResponse response;

    // pmLog('The categories search keyword is: "${widget.keyword}"');

    if (widget.keyword.isNotEmpty) {
      response = await _categoryListService.search(
        keyword: widget.keyword,
        limit: _loadMorePerPage,
        offset: isRefresh ? 0 : widget.searchData.offset,
        hideEmpty: true,
      );
    } else {
      response = await _categoryListService.fetch(
        limit: _loadMorePerPage,
        offset: isRefresh ? 0 : widget.searchData.offset,
        hideEmpty: true,
      );
    }

    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    _handleCategoryListResponse(response, isRefresh);

    if (!isRefresh && mounted) {
      SearchLoadMoreNotification(
        categorySearchScreen: SearchScreenLoadMoreStatus.idle,
      ).dispatch(context);
    }

    _isFetchingData = false;
  }

  void _handleCategoryListResponse(
    CategoryListResponse response,
    bool isRefresh,
  ) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    final searchCategoriesReactiveService = ref.read(
      searchCategoriesReactiveServiceProvider,
    );

    final bool isFirstLoad = _isFirstLoad();

    if (response.data.isEmpty) {
      if (isRefresh || isFirstLoad) {
        searchCategoriesReactiveService.clear();
      }

      ref
          .read(searchCategoriesDataProvider.notifier)
          .setLoadMoreEndReached(true);

      return;
    }

    if (response.data.length < _loadMorePerPage) {
      ref
          .read(searchCategoriesDataProvider.notifier)
          .setLoadMoreEndReached(true);
    } else {
      ref
          .read(searchCategoriesDataProvider.notifier)
          .setLoadMoreEndReached(false);
    }

    final currentOffset = isRefresh
        ? 0
        : ref.read(searchCategoriesDataProvider).offset;

    ref
        .read(searchCategoriesDataProvider.notifier)
        .setOffset(currentOffset + response.data.length);

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh || isFirstLoad) {
      searchCategoriesReactiveService.replaceAll(response.data);
    } else {
      searchCategoriesReactiveService.addItems(response.data);
    }
  }
}
