// liked_photos_screen.dart
// This screen displays photo feed

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/scroll_util.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/providers/liked_photos_provider.dart';
import 'package:portraitmode/photo/widgets/photo_list_item.dart';
import 'package:portraitmode/profile/services/profile_service.dart';

class LikedPhotosScreen extends ConsumerStatefulWidget {
  const LikedPhotosScreen({super.key});

  @override
  LikedPhotosScreenState createState() => LikedPhotosScreenState();
}

class LikedPhotosScreenState extends ConsumerState<LikedPhotosScreen> {
  final _scrollController = ScrollController();
  final _profileService = ProfileService();
  final int _loadMorePerPage = LoadMoreConfig.itemsPerPage;

  int _loadMoreLastId = 0;
  bool _loadMoreEndReached = false;

  final ValueNotifier<bool> _isFetchingNotifier = ValueNotifier(false);
  final ValueNotifier<bool> _blockScrollNotifier = ValueNotifier(false);

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _loadMore(false);
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);

    _scrollController.dispose();
    _isFetchingNotifier.dispose();
    _blockScrollNotifier.dispose();

    super.dispose();
  }

  double _getLoadMoreTriggerPoint(double maxExtent) {
    // Calculate how much of the content should remain when triggering load more
    double remainingContent = maxExtent * LoadMoreConfig.tresholdPercentage;

    // Apply min/max constraints
    remainingContent = remainingContent.clamp(
      LoadMoreConfig.minTreshold,
      LoadMoreConfig.maxTreshold,
    );

    // Return the scroll position that should trigger load more
    return maxExtent - remainingContent;
  }

  bool _canLoadMore() {
    return !_isFetchingNotifier.value && !_loadMoreEndReached;
  }

  void _onScroll() {
    // Safe some resource by early checking.
    if (!_canLoadMore()) return;

    double triggerPoint = _getLoadMoreTriggerPoint(
      _scrollController.position.maxScrollExtent,
    );

    // Handle load more when scrolling reaches the trigger point
    if (_scrollController.position.pixels >= triggerPoint) {
      if (_canLoadMore()) _loadMore(false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final List<PhotoData> photoList = ref.watch(likedPhotosProvider);

    return Scaffold(
      appBar: PmAppBar(
        scrollController: _scrollController,
        titleText: "Liked photos",
        useLogo: false,
        automaticallyImplyLeading: true,
      ),
      body: SafeArea(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 768.0),
          child: RefreshIndicator(
            color: context.colors.brandColor,
            elevation: 0.0,
            onRefresh: _handleRefresh,
            child: ValueListenableBuilder(
              valueListenable: _isFetchingNotifier,
              builder: (context, isFetching, child) {
                return ValueListenableBuilder(
                  valueListenable: _blockScrollNotifier,
                  builder: (context, blockScrolling, child) {
                    return ListView.builder(
                      controller: _scrollController,
                      physics: blockScrolling
                          ? const NeverScrollableScrollPhysics()
                          : null,
                      cacheExtent: getVerticalScrollCacheExtent(context),
                      itemCount:
                          photoList.length +
                          (isFetching && !_loadMoreEndReached ? 1 : 0),
                      itemBuilder: (BuildContext context, int index) {
                        if (index == photoList.length && isFetching) {
                          return Padding(
                            padding: EdgeInsets.only(
                              top: photoList.isEmpty ? 0 : 16.0,
                            ),
                            child: LinearProgressIndicator(
                              color: context.colors.baseColorAlt,
                            ),
                          );
                        }

                        return PhotoListItem(
                          key: ValueKey(photoList[index].id),
                          photo: photoList[index],
                          margin: EdgeInsets.only(
                            top: index == 0 ? LayoutConfig.contentTopGap : 12.0,
                          ),
                          screenName: 'liked_photos_screen',
                          onTwoFingersOn: () {
                            _blockScrollNotifier.value = true;
                          },
                          onTwoFingersOff: () {
                            _blockScrollNotifier.value = false;
                          },
                        );
                      },
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    _loadMoreEndReached = false;
    _loadMoreLastId = 0;

    _loadMore(true);
  }

  Future<void> _loadMore(bool isRefresh) async {
    _isFetchingNotifier.value = true;

    final PhotoListResponse response = await _profileService.likedPhotos(
      limit: _loadMorePerPage,
      lastId: _loadMoreLastId,
    );

    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    _handlePhotoListResponse(response, isRefresh);
    _isFetchingNotifier.value = false;
  }

  void _handlePhotoListResponse(PhotoListResponse response, bool isRefresh) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      }

      return;
    }

    final likedPhotosReactiveService = ref.read(
      likedPhotosReactiveServiceProvider,
    );

    final bool isFirstLoad = _loadMoreLastId == 0 || _loadMoreLastId == -1;

    if (response.data.isEmpty) {
      _loadMoreEndReached = true;

      if (isRefresh || isFirstLoad) {
        likedPhotosReactiveService.clear();
      }

      return;
    }

    if (response.data.length < _loadMorePerPage) {
      _loadMoreEndReached = true;
    }

    _loadMoreLastId = response.data.last.id;

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh || isFirstLoad) {
      likedPhotosReactiveService.replaceAll(response.data);
    } else {
      likedPhotosReactiveService.addItems(response.data);
    }
  }
}
