import 'package:flutter/widgets.dart';

enum FollowActionType { follow, unfollow }

extension AssignmentActionEnumExtension on FollowActionType {
  String get text {
    switch (this) {
      case FollowActionType.follow:
        return 'follow';
      case FollowActionType.unfollow:
        return 'unfollow';
    }
  }
}

enum BlockActionType { block, unblock }

extension BlockActionEnumExtension on BlockActionType {
  String get text {
    switch (this) {
      case BlockActionType.block:
        return 'block';
      case BlockActionType.unblock:
        return 'unblock';
    }
  }
}

@immutable
class AlbumLoadMoreNotification extends Notification {
  final Map<String, bool> albums;

  const AlbumLoadMoreNotification({this.albums = const {}});

  AlbumLoadMoreNotification copyWith({
    required String key,
    required bool value,
  }) {
    if (albums.containsKey(key)) {
      albums[key] = value;
    }

    albums.addAll({key: value});

    return AlbumLoadMoreNotification(albums: albums);
  }

  bool get isAnyAlbumDoingLoadMore => albums.values.any((value) => value);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! AlbumLoadMoreNotification) return false;

    return other.albums == albums;
  }

  @override
  int get hashCode => albums.hashCode;

  @override
  String toString() {
    return '''
AlbumLoadMoreNotification(
  albums: $albums,
)
''';
  }
}
