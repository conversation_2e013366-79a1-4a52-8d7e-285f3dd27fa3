import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';

class HomeTabbar extends StatefulWidget {
  final TabController? controller;

  const HomeTabbar({super.key, this.controller});

  @override
  HomeTabbarState createState() => HomeTabbarState();
}

class HomeTabbarState extends State<HomeTabbar>
    with SingleTickerProviderStateMixin {
  final double _xPadding = 20.0;

  @override
  Widget build(BuildContext context) {
    // double tabWidth = containerWidth / 5;
    // pmLog('🪟 Building HomeTabbar');

    return Container(
      padding: EdgeInsets.symmetric(horizontal: _xPadding),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: context.colors.scaffoldColor,
        border: Border(
          bottom: BorderSide(
            color: context.isDarkMode
                ? AppColorsCache.dark().baseColorAlt
                : AppColorsCache.light().baseColor,
            width: AppConfig.appBarBorderWidth,
          ),
        ),
      ),
      child: TabBar(
        controller: widget.controller,
        isScrollable: false,
        dividerHeight: 0.0,
        indicatorPadding: EdgeInsets.only(bottom: -1.0),
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(width: 2.0, color: context.colors.brandColor),
          borderRadius: BorderRadius.zero, // This removes the rounded corners
        ),
        labelColor: context.isDarkMode
            ? AppColorsCache.dark().brandColor
            : AppColorsCache.light().brandColorAlt,
        labelPadding: const EdgeInsets.symmetric(
          horizontal: TabbarConfig.labelXPadding,
        ),
        unselectedLabelColor: context.colors.primarySwatch[300],
        tabAlignment: TabAlignment.fill,
        tabs: const [
          Tab(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.0),
              child: Text('Recent photos'),
            ),
          ),
          Tab(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.0),
              child: Text('Following'),
            ),
          ),
        ],
      ),
    );
  }
}
