// photo_store_provider.dart

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/comment/providers/comment_list_provider.dart';
import 'package:portraitmode/comment/providers/comment_store_provider.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';

/// A highly optimized and memory-efficient notifier for managing photos by ID.
///
/// Uses `Map<int, PhotoData>` for O(1) operations and eliminates the need for
/// index caching and list copying that plagued the previous implementation.
final class PhotoStoreNotifier
    extends AutoDisposeNotifier<Map<int, PhotoData>> {
  @override
  Map<int, PhotoData> build() {
    return <int, PhotoData>{};
  }

  /// Creates a new state map to trigger provider updates.
  /// Only creates new map if there are actual changes.
  void _updateState(Map<int, PhotoData> Function(Map<int, PhotoData>) updater) {
    final newState = updater(Map<int, PhotoData>.from(state));

    if (!mapEquals(state, newState)) {
      state = newState;
    }
  }

  /// Gets a photo by ID. Returns null if not found.
  PhotoData? getItem(int id) {
    return state[id];
  }

  /// Gets multiple photos by their IDs.
  /// Returns only the photos that exist, maintaining the order of requested IDs.
  List<PhotoData> getItems(List<int> ids) {
    if (ids.isEmpty) return const [];

    final result = <PhotoData>[];

    for (final id in ids) {
      final photo = state[id];

      if (photo != null) {
        result.add(photo);
      }
    }

    return result;
  }

  /// Checks if a photo with the given ID exists.
  bool hasItem(int id) {
    return state.containsKey(id);
  }

  /// Adds a new photo only if it doesn't already exist.
  ///
  /// If [updateIfExists] is true, will update the existing photo.
  void addItem(PhotoData newItem, {bool updateIfExists = true}) {
    if (state.containsKey(newItem.id)) {
      if (updateIfExists) updateItem(newItem);
      return;
    }

    _updateState((map) {
      map[newItem.id] = newItem;
      return map;
    });
  }

  /// Adds multiple photos efficiently.
  ///
  /// If [updateIfExists] is true, will update existing photos.
  void addItems(List<PhotoData> newItems, {bool updateIfExists = true}) {
    if (newItems.isEmpty) return;

    _updateState((map) {
      for (final item in newItems) {
        if (map.containsKey(item.id)) {
          if (updateIfExists) {
            map[item.id] = item;
          }
        } else {
          map[item.id] = item;
        }
      }
      return map;
    });
  }

  /// Updates a single existing photo.
  ///
  /// If [addIfNotExists] is true, will add the photo if it doesn't exist.
  void updateItem(PhotoData newItem, {bool addIfNotExists = true}) {
    if (!state.containsKey(newItem.id)) {
      if (addIfNotExists) {
        addItem(newItem, updateIfExists: false);
      }
      return;
    }

    // Only update if the photo has actually changed
    if (state[newItem.id] == newItem) return;

    _updateState((map) {
      map[newItem.id] = newItem;
      return map;
    });
  }

  /// Updates multiple photos efficiently.
  ///
  /// If [addIfNotExists] is true, will add photos that don't exist.
  void updateItems(List<PhotoData> newItems, {bool addIfNotExists = true}) {
    if (newItems.isEmpty) return;

    _updateState((map) {
      for (final item in newItems) {
        if (map.containsKey(item.id)) {
          map[item.id] = item;
        } else if (addIfNotExists) {
          map[item.id] = item;
        }
      }
      return map;
    });
  }

  /// Removes a photo by ID and cleans up any related data.
  void removeItem(int id) {
    if (!state.containsKey(id)) return;

    _updateState((map) {
      map.remove(id);
      return map;
    });

    // Clean up related data
    try {
      ref.read(commentStoreProvider.notifier).removeByPhotoId(id);
      ref.read(commentListManagerProvider.notifier).clearByPhotoId(id);
    } catch (error) {
      if (kDebugMode) {
        debugPrint('Error cleaning up related data for photo $id: $error');
      }
    }
  }

  /// Removes multiple photos by their IDs and cleans related data.
  void removeItems(List<int> ids) {
    if (ids.isEmpty) return;

    // Filter to only IDs that actually exist
    final existingIds = ids.where((id) => state.containsKey(id)).toList();
    if (existingIds.isEmpty) return;

    _updateState((map) {
      for (final id in existingIds) {
        map.remove(id);
      }
      return map;
    });

    // Clean up related data
    try {
      final commentStore = ref.read(commentStoreProvider.notifier);
      final commentListManager = ref.read(commentListManagerProvider.notifier);

      for (final id in existingIds) {
        commentStore.removeByPhotoId(id);
        commentListManager.clearByPhotoId(id);
      }
    } catch (error) {
      if (kDebugMode) {
        debugPrint(
          'Error cleaning up related data for photos $existingIds: $error',
        );
      }
    }
  }

  /// Removes all photos created by a specific author.
  void removeByAuthorId(int authorId) {
    final photosToRemove = state.values
        .where((photo) => photo.authorId == authorId)
        .map((photo) => photo.id)
        .toList();

    if (photosToRemove.isEmpty) return;

    _updateState((map) {
      for (final photoId in photosToRemove) {
        map.remove(photoId);
      }
      return map;
    });

    try {
      ref.read(commentStoreProvider.notifier).removeByAuthorId(authorId);
    } catch (error) {
      if (kDebugMode) {
        debugPrint('Error cleaning up comments for author $authorId: $error');
      }
    }
  }

  /// Replaces the entire store with a new collection of photos.
  void replaceAll(List<PhotoData> newList) {
    final newMap = <int, PhotoData>{};
    for (final photo in newList) {
      newMap[photo.id] = photo;
    }
    state = newMap;
  }

  /// Clears all photos in the store.
  void clear() {
    if (state.isNotEmpty) {
      state = <int, PhotoData>{};
    }
  }

  // ------------------------------------------------------------
  // Specialized update methods for single photo fields
  // These are now much more efficient with direct map access
  // ------------------------------------------------------------

  /// Updates a specific field of a photo if it exists.
  void _updatePhotoField(int photoId, PhotoData Function(PhotoData) updater) {
    final currentPhoto = state[photoId];
    if (currentPhoto == null) return;

    final updatedPhoto = updater(currentPhoto);

    if (currentPhoto != updatedPhoto) {
      _updateState((map) {
        map[photoId] = updatedPhoto;
        return map;
      });
    }
  }

  void setPotd(int photoId, bool isPotd) {
    _updatePhotoField(photoId, (photo) => photo.copyWith(potd: isPotd));
  }

  void setFeatured(int photoId, bool isFeatured) {
    _updatePhotoField(photoId, (photo) => photo.copyWith(featured: isFeatured));
  }

  void setTotalLikes(int photoId, int newTotalLikes) {
    _updatePhotoField(
      photoId,
      (photo) => photo.copyWith(totalLikes: newTotalLikes),
    );
  }

  void setIsLiked(int photoId, bool isLiked) {
    _updatePhotoField(photoId, (photo) => photo.copyWith(isLiked: isLiked));
  }

  void setTotalComments(int photoId, int newTotalComments) {
    _updatePhotoField(
      photoId,
      (photo) => photo.copyWith(totalComments: newTotalComments),
    );
  }

  void addTotalComments(int photoId, int amount) {
    if (amount == 0) return;
    _updatePhotoField(
      photoId,
      (photo) => photo.copyWith(totalComments: photo.totalComments + amount),
    );
  }

  void incrementTotalComments(int photoId) {
    _updatePhotoField(
      photoId,
      (photo) => photo.copyWith(totalComments: photo.totalComments + 1),
    );
  }

  void decrementTotalComments(int photoId) {
    _updatePhotoField(
      photoId,
      (photo) => photo.copyWith(
        totalComments: (photo.totalComments - 1)
            .clamp(0, double.infinity)
            .toInt(),
      ),
    );
  }

  // ------------------------------------------------------------
  // Utility getters and methods
  // ------------------------------------------------------------

  /// Returns total number of photos
  int get count => state.length;

  /// Checks if the store is empty
  bool get isEmpty => state.isEmpty;

  /// Checks if the store has at least one photo
  bool get isNotEmpty => state.isNotEmpty;

  /// Returns all photo IDs
  List<int> get allIds => state.keys.toList();

  /// Returns all photos as a list
  List<PhotoData> get allPhotos => state.values.toList();

  /// Returns all photos by a specific author
  List<PhotoData> getPhotosByAuthor(int authorId) {
    return state.values.where((photo) => photo.authorId == authorId).toList();
  }

  /// Returns all photos by multiple authors
  List<PhotoData> getPhotosByAuthors(Set<int> authorIds) {
    if (authorIds.isEmpty) return const [];
    return state.values
        .where((photo) => authorIds.contains(photo.authorId))
        .toList();
  }
}

/// NotifierProvider for the main photo store
final photoStoreProvider =
    NotifierProvider.autoDispose<PhotoStoreNotifier, Map<int, PhotoData>>(
      PhotoStoreNotifier.new,
    );

/// A selective provider that only rebuilds when a specific photo changes
final photoProvider = Provider.family.autoDispose<PhotoData?, int>((
  ref,
  photoId,
) {
  return ref.watch(photoStoreProvider.select((photos) => photos[photoId]));
});

/// A selective provider for specific photo properties to minimize rebuilds
final photoPropsProvider =
    Provider.family<
      ({
        bool? potd,
        bool? featured,
        int? totalLikes,
        int? totalComments,
        bool? isLiked,
      }),
      int
    >((ref, photoId) {
      final photo = ref.watch(photoStoreProvider)[photoId];

      return (
        potd: photo?.potd,
        featured: photo?.featured,
        totalLikes: photo?.totalLikes,
        totalComments: photo?.totalComments,
        isLiked: photo?.isLiked,
      );
    });

/// Provider that returns the current total photo count
final photoCountProvider = Provider.autoDispose<int>((ref) {
  return ref.watch(photoStoreProvider.select((photos) => photos.length));
});

/// Provider that checks if the store has any photos
final hasPhotosProvider = Provider.autoDispose<bool>((ref) {
  return ref.watch(photoStoreProvider.select((photos) => photos.isNotEmpty));
});

/// Provider that returns all photos as a list (for UI components that need lists)
final photoListProvider = Provider.autoDispose<List<PhotoData>>((ref) {
  return ref.watch(
    photoStoreProvider.select((photos) => photos.values.toList()),
  );
});

/// Provider that returns all photos by a given author ID
final photosByAuthorProvider = Provider.family
    .autoDispose<List<PhotoData>, int>((ref, authorId) {
      return ref.watch(
        photoStoreProvider.select(
          (photos) => photos.values
              .where((photo) => photo.authorId == authorId)
              .toList(),
        ),
      );
    });

/// Provider that checks if a specific photo ID exists
final hasPhotoProvider = Provider.family.autoDispose<bool, int>((ref, photoId) {
  return ref.watch(
    photoStoreProvider.select((photos) => photos.containsKey(photoId)),
  );
});
