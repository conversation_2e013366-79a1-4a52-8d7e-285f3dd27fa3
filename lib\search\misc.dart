import 'package:flutter/widgets.dart';

enum SearchScreenItem {
  photoSearchScreen,
  categorySearchScreen,
  cameraSearchScreen,
  artistSearchScreen,
}

enum SearchScreenLoadMoreStatus {
  idle,
  doingFirstLoadMore,
  doingLoadMore,
  loadMoreEndReached,
}

@immutable
class SearchLoadMoreNotification extends Notification {
  final SearchScreenLoadMoreStatus photoSearchScreen;
  final SearchScreenLoadMoreStatus categorySearchScreen;
  final SearchScreenLoadMoreStatus cameraSearchScreen;
  final SearchScreenLoadMoreStatus artistSearchScreen;

  const SearchLoadMoreNotification({
    this.photoSearchScreen = SearchScreenLoadMoreStatus.idle,
    this.categorySearchScreen = SearchScreenLoadMoreStatus.idle,
    this.cameraSearchScreen = SearchScreenLoadMoreStatus.idle,
    this.artistSearchScreen = SearchScreenLoadMoreStatus.idle,
  });

  SearchLoadMoreNotification copyWith({
    SearchScreenLoadMoreStatus? photoSearchScreen,
    SearchScreenLoadMoreStatus? categorySearchScreen,
    SearchScreenLoadMoreStatus? cameraSearchScreen,
    SearchScreenLoadMoreStatus? artistSearchScreen,
  }) {
    return SearchLoadMoreNotification(
      photoSearchScreen: photoSearchScreen ?? this.photoSearchScreen,
      categorySearchScreen: categorySearchScreen ?? this.categorySearchScreen,
      cameraSearchScreen: cameraSearchScreen ?? this.cameraSearchScreen,
      artistSearchScreen: artistSearchScreen ?? this.artistSearchScreen,
    );
  }

  List<SearchScreenItem> screensDoingLoadMore() {
    final screens = <SearchScreenItem>[];

    if (photoSearchScreen == SearchScreenLoadMoreStatus.doingLoadMore ||
        photoSearchScreen == SearchScreenLoadMoreStatus.doingFirstLoadMore) {
      screens.add(SearchScreenItem.photoSearchScreen);
    }

    if (categorySearchScreen == SearchScreenLoadMoreStatus.doingLoadMore ||
        categorySearchScreen == SearchScreenLoadMoreStatus.doingFirstLoadMore) {
      screens.add(SearchScreenItem.categorySearchScreen);
    }

    if (cameraSearchScreen == SearchScreenLoadMoreStatus.doingLoadMore ||
        cameraSearchScreen == SearchScreenLoadMoreStatus.doingFirstLoadMore) {
      screens.add(SearchScreenItem.cameraSearchScreen);
    }

    if (artistSearchScreen == SearchScreenLoadMoreStatus.doingLoadMore ||
        artistSearchScreen == SearchScreenLoadMoreStatus.doingFirstLoadMore) {
      screens.add(SearchScreenItem.artistSearchScreen);
    }

    return screens;
  }

  bool get isAnyScreenDoingFirstLoadMore => [
    photoSearchScreen,
    categorySearchScreen,
    cameraSearchScreen,
    artistSearchScreen,
  ].any((status) => status == SearchScreenLoadMoreStatus.doingFirstLoadMore);

  bool get isAnyScreenDoingLoadMore =>
      [
        photoSearchScreen,
        categorySearchScreen,
        cameraSearchScreen,
        artistSearchScreen,
      ].any(
        (status) =>
            status == SearchScreenLoadMoreStatus.doingLoadMore ||
            status == SearchScreenLoadMoreStatus.doingFirstLoadMore,
      );

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! SearchLoadMoreNotification) return false;

    return other.photoSearchScreen == photoSearchScreen &&
        other.categorySearchScreen == categorySearchScreen &&
        other.cameraSearchScreen == cameraSearchScreen &&
        other.artistSearchScreen == artistSearchScreen;
  }

  @override
  int get hashCode => Object.hash(
    photoSearchScreen,
    categorySearchScreen,
    cameraSearchScreen,
    artistSearchScreen,
  );

  @override
  String toString() {
    return '''
SearchLoadMoreNotification(
  photoSearchScreen: $photoSearchScreen,
  categorySearchScreen: $categorySearchScreen,
  cameraSearchScreen: $cameraSearchScreen,
  artistSearchScreen: $artistSearchScreen,
)
''';
  }
}
