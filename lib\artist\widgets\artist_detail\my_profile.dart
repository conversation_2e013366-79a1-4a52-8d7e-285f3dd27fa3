import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/album/dto/album_data.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/log_util.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/appbar/profile_menu_indicator.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/misc.dart';
import 'package:portraitmode/artist/widgets/artist_detail/my_profile/album_tabbar_delegate.dart';
import 'package:portraitmode/artist/widgets/artist_detail/my_profile/my_album_tab_content_item.dart';
import 'package:portraitmode/artist/widgets/artist_detail/profile_header.dart';
import 'package:portraitmode/profile/providers/my_album_provider.dart';
import 'package:portraitmode/settings/widgets/settings_screen.dart';

class MyProfile extends ConsumerStatefulWidget {
  const MyProfile({
    super.key,
    this.useBackButton = true,
    required this.containerWidth,
    required this.artist,
  });

  final bool useBackButton;
  final double containerWidth;
  final ArtistData artist;

  @override
  MyProfileState createState() => MyProfileState();
}

class MyProfileState extends ConsumerState<MyProfile> {
  final _scrollController = ScrollController();
  final ValueNotifier<bool> _isLoadingMoreNotifier = ValueNotifier(false);

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _isLoadingMoreNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final albums = ref.watch(myAlbumProvider);
    final albumList = albums.isNotEmpty ? albums : (widget.artist.albums ?? []);

    pmLog('🖥️ Building MyProfile');

    return DefaultTabController(
      length: albumList.length,
      child: Scaffold(
        backgroundColor: context.colors.lightColor,
        body: SafeArea(
          child: NestedScrollView(
            controller: _scrollController,
            headerSliverBuilder:
                (BuildContext context, bool innerBoxIsScrolled) {
                  return [
                    PmSliverAppBar(
                      scrollController: _scrollController,
                      automaticallyImplyLeading: widget.useBackButton,
                      titleText: widget.useBackButton
                          ? '@${widget.artist.nicename}'
                          : '',
                      useLogo: widget.useBackButton ? false : true,
                      backgroundColor: context.colors.lightColor,
                      floating: false,
                      pinned: true,
                      actions: [
                        ProfileMenuIndicator(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const SettingsScreen(),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                    SliverToBoxAdapter(
                      child: Container(
                        padding: const EdgeInsets.only(
                          top: LayoutConfig.contentTopGap,
                          right: ScreenStyleConfig.horizontalPadding,
                          bottom: ScreenStyleConfig.verticalPadding + 20.0,
                          left: ScreenStyleConfig.horizontalPadding,
                        ),
                        child: ProfileHeader(artist: widget.artist),
                      ),
                    ),
                    SliverPersistentHeader(
                      floating: false,
                      pinned: true,
                      delegate: AlbumTabbarDelegate(albumList: albumList),
                    ),
                  ];
                },
            body: Container(
              color: context.colors.scaffoldColor,
              child: Column(
                children: [
                  Expanded(
                    child: NotificationListener<AlbumLoadMoreNotification>(
                      onNotification: (albums) {
                        if (albums.isAnyAlbumDoingLoadMore) {
                          if (!_isLoadingMoreNotifier.value) {
                            _isLoadingMoreNotifier.value = true;
                          }
                        } else {
                          if (_isLoadingMoreNotifier.value) {
                            _isLoadingMoreNotifier.value = false;
                          }
                        }

                        return false;
                      },
                      child: TabBarView(
                        children: <MyAlbumTabContentItem>[
                          // Build AlbumTabContent widget using for loop.
                          for (AlbumData album in albumList)
                            MyAlbumTabContentItem(
                              loadMoreNotifier: _isLoadingMoreNotifier,
                              containerWidth: widget.containerWidth,
                              artistId: widget.artist.id,
                              album: album,
                            ),
                        ],
                      ),
                    ),
                  ),
                  ValueListenableBuilder(
                    valueListenable: _isLoadingMoreNotifier,
                    builder: (context, isLoadingMore, child) {
                      if (isLoadingMore) {
                        return Stack(
                          alignment: Alignment.bottomCenter,
                          children: [
                            SizedBox(
                              height: 2.7,
                              child: LinearProgressIndicator(
                                color: context.colors.baseColorAlt,
                              ),
                            ),
                          ],
                        );
                      }

                      return SizedBox.shrink();
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
