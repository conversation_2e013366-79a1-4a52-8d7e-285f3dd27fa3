// photo_detail_screen.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/appbar/profile_menu_indicator.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/comment/dto/comment_data.dart';
import 'package:portraitmode/comment/http_responses/comment_list_response.dart';
import 'package:portraitmode/comment/providers/comment_activity_provider.dart';
import 'package:portraitmode/comment/providers/comment_list_provider.dart';
import 'package:portraitmode/comment/services/comment_list_service.dart';
import 'package:portraitmode/comment/utils/delete_comment_util.dart';
import 'package:portraitmode/comment/utils/edit_comment_util.dart';
import 'package:portraitmode/comment/widgets/comment_form.dart';
import 'package:portraitmode/comment/widgets/comment_list_item.dart';
import 'package:portraitmode/common/enum.dart';
import 'package:portraitmode/common/utils/snackbar_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/max_width/max_width.dart';
import 'package:portraitmode/moderation/utils/featured_assignment_util.dart';
import 'package:portraitmode/moderation/utils/potd_assignment_util.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/utils/archive_assignment_util.dart';
import 'package:portraitmode/photo/utils/photo_deletion_util.dart';
import 'package:portraitmode/photo/widgets/bottom_sheets/other_photo_bottom_sheet.dart';
import 'package:portraitmode/photo/widgets/bottom_sheets/own_photo_bottom_sheet.dart';
import 'package:portraitmode/photo/widgets/photo_detail/photo_author.dart';
import 'package:portraitmode/photo/widgets/photo_detail/photo_categories.dart';
import 'package:portraitmode/photo/widgets/photo_detail/photo_detail_description.dart';
import 'package:portraitmode/photo/widgets/photo_detail/photo_detail_frame.dart';
import 'package:portraitmode/photo/widgets/photo_detail/photo_metadata.dart';

class PhotoDetailScreen extends ConsumerStatefulWidget {
  final PhotoData photo;
  final String originScreenName;
  final bool isPhotoDetail;

  const PhotoDetailScreen({
    super.key,
    required this.photo,
    required this.originScreenName,
    this.isPhotoDetail = true,
  });

  @override
  PhotoDetailScreenState createState() => PhotoDetailScreenState();
}

class PhotoDetailScreenState extends ConsumerState<PhotoDetailScreen> {
  final _scrollController = ScrollController();
  final _commentListService = CommentListService();
  final _commentFieldController = TextEditingController();
  final _focusNode = FocusNode();

  final int _loadMorePerPage = LoadMoreConfig.itemsPerPage;
  int _pageIndex = 0;
  bool _loadMoreEndReached = false;

  final _photoDescriptionKey = GlobalKey();
  int? _activeCommentId;

  final _isFetchingNotifier = ValueNotifier(false);
  final ValueNotifier<Map<int, GlobalKey>> _activeCommentKeysNotifier =
      ValueNotifier({});
  late final ValueNotifier<bool> _isArchivedNotifier;
  final _isProcessingNotifier = ValueNotifier(false);

  final double _sectionSpacing = 20.0;
  late final int _profileId;
  late bool _isOwnPhoto;

  @override
  void initState() {
    super.initState();

    _isArchivedNotifier = ValueNotifier(
      widget.originScreenName == 'archived_photos_screen',
    );

    _profileId = LocalUserService.userId ?? 0;
    _isOwnPhoto = widget.photo.authorId == _profileId;

    _scrollController.addListener(_onScroll);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _loadMore(false);
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _activeCommentKeysNotifier.value.clear();

    _commentFieldController.dispose();
    _focusNode.dispose();
    _scrollController.dispose();
    _isFetchingNotifier.dispose();
    _activeCommentKeysNotifier.dispose();
    _isArchivedNotifier.dispose();
    _isProcessingNotifier.dispose();

    super.dispose();
  }

  double _getLoadMoreTriggerPoint(double maxExtent) {
    // Calculate how much of the content should remain when triggering load more
    double remainingContent = maxExtent * LoadMoreConfig.tresholdPercentage;

    // Apply min/max constraints
    remainingContent = remainingContent.clamp(
      LoadMoreConfig.minTreshold,
      LoadMoreConfig.maxTreshold,
    );

    // Return the scroll position that should trigger load more
    return maxExtent - remainingContent;
  }

  bool _canLoadMore() {
    return !_isFetchingNotifier.value && !_loadMoreEndReached;
  }

  void _onScroll() {
    // Safe some resource by early checking.
    if (!_canLoadMore()) return;

    double triggerPoint = _getLoadMoreTriggerPoint(
      _scrollController.position.maxScrollExtent,
    );

    // Handle load more when scrolling reaches the trigger point
    if (_scrollController.position.pixels >= triggerPoint) {
      if (_canLoadMore()) _loadMore(false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final PhotoData photo =
        ref.watch(photoProvider(widget.photo.id)) ?? widget.photo;

    final List<CommentData> commentList = ref.watch(
      commentListProvider(photo.id),
    );

    bool hasDescription = photo.description.isNotEmpty;

    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: RefreshIndicator(
                edgeOffset: LayoutConfig.bottomNavBarHeight,
                color: context.colors.brandColor,
                elevation: 0.0,
                onRefresh: _handleRefresh,
                child: CustomScrollView(
                  controller: _scrollController,
                  slivers: [
                    PmSliverAppBar(
                      scrollController: _scrollController,
                      titleText: widget.isPhotoDetail
                          ? "Photo details"
                          : "Comments",
                      useLogo: false,
                      automaticallyImplyLeading: true,
                      actions: [
                        ValueListenableBuilder(
                          valueListenable: _isArchivedNotifier,
                          builder: (context, isArchived, child) {
                            return ProfileMenuIndicator(
                              onTap: () {
                                if (_isOwnPhoto) {
                                  _showOwnPhotoBottomSheet(
                                    photo,
                                    archiveAssignmentAction: isArchived
                                        ? AssignmentActionType.unassign
                                        : AssignmentActionType.assign,
                                    onArchivedStatusChanged: () {
                                      _isArchivedNotifier.value = !isArchived;
                                    },
                                  );

                                  return;
                                }

                                _showOtherPhotoBottomSheet(photo);
                              },
                            );
                          },
                        ),
                      ],
                    ),
                    if (widget.isPhotoDetail)
                      SliverToBoxAdapter(
                        child: Align(
                          alignment: Alignment.topCenter,
                          child: ValueListenableBuilder(
                            valueListenable: _isProcessingNotifier,
                            builder: (context, isProcessing, child) {
                              return Container(
                                constraints: const BoxConstraints(
                                  maxWidth: 768.0,
                                ),
                                child: PhotoDetailFrame(
                                  photo: photo,
                                  isProcessing: isProcessing,
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    if (hasDescription)
                      SliverToBoxAdapter(
                        child: PhotoDetailDescription(
                          key: _photoDescriptionKey,
                          photo: photo,
                          padding: EdgeInsets.only(
                            right: ScreenStyleConfig.horizontalPadding,
                            left: ScreenStyleConfig.horizontalPadding,
                            top: _sectionSpacing,
                          ),
                          contentToDividerGap: _sectionSpacing,
                        ),
                      ),
                    if (widget.isPhotoDetail)
                      SliverToBoxAdapter(
                        child: MaxWidth(
                          maxWidth: 768.0,
                          child: PhotoAuthor(
                            authorId: photo.authorId,
                            authorNicename: photo.authorNicename,
                            authorDisplayName: photo.authorDisplayName,
                            authorProfileUrl: photo.authorProfileUrl,
                            authorAvatarUrl: photo.authorAvatarUrl,
                            authorMembershipType: photo.authorMembershipType,
                            padding: EdgeInsets.only(
                              right: ScreenStyleConfig.horizontalPadding,
                              left: ScreenStyleConfig.horizontalPadding,
                              top: _sectionSpacing,
                            ),
                          ),
                        ),
                      ),
                    if (widget.isPhotoDetail)
                      SliverToBoxAdapter(
                        child: Container(
                          constraints: const BoxConstraints(maxWidth: 768.0),
                          child: LayoutBuilder(
                            builder: (context, constraints) {
                              return PhotoMetadata(
                                photo: photo,
                                padding: EdgeInsets.only(
                                  left: ScreenStyleConfig.horizontalPadding,
                                  right: ScreenStyleConfig.horizontalPadding,
                                  top: _sectionSpacing,
                                ),
                                contentToDividerGap: _sectionSpacing,
                              );
                            },
                          ),
                        ),
                      ),
                    if (widget.isPhotoDetail && photo.categories.isNotEmpty)
                      SliverToBoxAdapter(
                        child: PhotoCategories(
                          categoryIds: photo.categories,
                          padding: EdgeInsets.only(
                            top: _sectionSpacing,
                            left: ScreenStyleConfig.horizontalPadding,
                            right: ScreenStyleConfig.horizontalPadding,
                          ),
                          contentToDividerGap: _sectionSpacing,
                        ),
                      ),
                    if (widget.isPhotoDetail)
                      SliverToBoxAdapter(
                        child: Padding(
                          padding: EdgeInsets.only(
                            top: _sectionSpacing,
                            left: ScreenStyleConfig.horizontalPadding,
                            right: ScreenStyleConfig.horizontalPadding,
                          ),
                          child: const Text(
                            "Comments",
                            style: TextStyle(
                              fontSize: 15.0,
                              // fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    SliverToBoxAdapter(
                      child: SizedBox(height: _sectionSpacing),
                    ),
                    SliverList.builder(
                      itemCount: commentList.length,
                      itemBuilder: (BuildContext context, int index) {
                        double marginBottom = 12.0;
                        final bool isOwnComment =
                            commentList[index].authorId == _profileId;

                        return CommentListItem(
                          key: ValueKey('comment_${commentList[index].id}'),
                          commentFieldController: _commentFieldController,
                          commentFieldFocusNode: _focusNode,
                          comment: commentList[index],
                          isOwnComment: isOwnComment,
                          padding: EdgeInsets.only(bottom: marginBottom),
                          onEditCommentTap: isOwnComment
                              ? () {
                                  final int commentId = commentList[index].id;

                                  _activeCommentId = commentId;

                                  final Map<int, GlobalKey> activeCommentKeys =
                                      Map.from(
                                        _activeCommentKeysNotifier.value,
                                      );

                                  activeCommentKeys[commentId] = GlobalKey();

                                  _activeCommentKeysNotifier.value =
                                      activeCommentKeys;

                                  handleEditCommentTap(
                                    ref: ref,
                                    comment: commentList[index],
                                    commentFieldController:
                                        _commentFieldController,
                                    commentFieldFocusNode: _focusNode,
                                  );
                                }
                              : null,
                          onDeleteCommentTap: isOwnComment
                              ? () async {
                                  final int commentId = commentList[index].id;
                                  final GlobalKey? targetKey =
                                      _activeCommentKeysNotifier
                                          .value[commentId];

                                  if (targetKey == null) {
                                    return;
                                  }

                                  DeleteCommentUtil(
                                    context: context,
                                    ref: ref,
                                    photoId: photo.id,
                                    commentToDelete: commentList[index],
                                    targetKey: targetKey,
                                  ).handleDeleteEvent();
                                }
                              : null,
                        );
                      },
                    ),
                    SliverToBoxAdapter(
                      child: ValueListenableBuilder(
                        valueListenable: _isFetchingNotifier,
                        builder: (context, isFetching, child) {
                          if (isFetching && !_loadMoreEndReached) {
                            return Container(
                              padding: const EdgeInsets.all(16.0),
                              alignment: Alignment.center,
                              child: CircularProgressIndicator(
                                color: context.colors.baseColorAlt,
                              ),
                            );
                          }

                          if (!isFetching &&
                              _loadMoreEndReached &&
                              commentList.isEmpty) {
                            return Container(
                              padding: const EdgeInsets.all(16.0),
                              alignment: Alignment.center,
                              child: Text(
                                'No comments found',
                                style: TextStyle(
                                  color: context.colors.primarySwatch[300],
                                ),
                              ),
                            );
                          }

                          return SizedBox.shrink();
                        },
                      ),
                    ),
                    if (commentList.isEmpty)
                      const SliverToBoxAdapter(child: SizedBox(height: 30.0)),
                  ],
                ),
              ),
            ),
            ValueListenableBuilder(
              valueListenable: _activeCommentKeysNotifier,
              builder: (context, activeCommentKeys, child) {
                return CommentForm(
                  photoId: photo.id,
                  fieldController: _commentFieldController,
                  focusNode: _focusNode,
                  photoDescriptionKey: _photoDescriptionKey,
                  activeCommentKey: activeCommentKeys[_activeCommentId],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    _loadMoreEndReached = false;
    _pageIndex = 0;
    ref.read(commentActivityProvider.notifier).reset();
    _loadMore(true);
  }

  Future<void> _loadMore(bool isRefresh) async {
    _isFetchingNotifier.value = true;

    final CommentListResponse response = await _commentListService.fetch(
      isRefreshAction: isRefresh,
      postId: widget.photo.id,
      limit: _loadMorePerPage,
      offset: _pageIndex * _loadMorePerPage,
    );

    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    _handleCommentListResponse(response, isRefresh);
    _isFetchingNotifier.value = false;
  }

  void _handleCommentListResponse(
    CommentListResponse response,
    bool isRefresh,
  ) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      }

      return;
    }

    final photoCommentService = ref.read(photoCommentReactiveServiceProvider);

    final bool isFirstLoad = _pageIndex == 0 || _pageIndex == -1;

    if (response.data.isEmpty) {
      _loadMoreEndReached = true;

      if (isRefresh || isFirstLoad) {
        photoCommentService.clearPhotoComments(widget.photo.id);
      }

      return;
    }

    if (response.data.length < _loadMorePerPage) {
      _loadMoreEndReached = true;
    }

    _pageIndex++;

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh || isFirstLoad) {
      photoCommentService.replaceAllComments(widget.photo.id, response.data);
    } else {
      photoCommentService.prependComments(widget.photo.id, response.data);
    }
  }

  void _showOtherPhotoBottomSheet(PhotoData photo) {
    showModalBottomSheet(
      context: context,
      useSafeArea: true,
      builder: (BuildContext context) {
        return OtherPhotoBottomSheet(
          photo: photo,
          isOwnPhoto: _isOwnPhoto,
          screenName: 'photo_detail_screen',
          onDeletePhotoByMod: (PhotoData photo) {
            _handleDeletePhoto(photo, isModeration: true);
          },
          onFeaturedAssignment:
              ({
                required AssignmentActionType action,
                bool notifyAuthor = false,
              }) {
                _handleFeaturedAssignment(
                  photo,
                  action: action,
                  notifyAuthor: notifyAuthor,
                );
              },
          onPotdAssignment: ({required AssignmentActionType action}) {
            _handlePotdAssignment(photo, action: action);
          },
        );
      },
    );
  }

  void _showOwnPhotoBottomSheet(
    PhotoData photo, {
    AssignmentActionType? archiveAssignmentAction,
    VoidCallback? onArchivedStatusChanged,
  }) {
    showModalBottomSheet(
      context: context,
      useSafeArea: true,
      builder: (BuildContext context) {
        return OwnPhotoBottomSheet(
          photo: photo,
          screenName: widget.originScreenName,
          archiveAssignmentType: archiveAssignmentAction,
          showExtraMenu: false,
          onPhotoArchiveAssignment:
              (AssignmentActionType actionType, PhotoData photo) {
                _handlePhotoArchiveAssignment(
                  photo: photo,
                  actionType: actionType,
                  onArchivedStatusChanged: onArchivedStatusChanged,
                );
              },
          onDeletePhoto: (PhotoData photo) {
            _handleDeletePhoto(photo, isModeration: false);
          },
          onFeaturedAssignment:
              ({
                required AssignmentActionType action,
                bool notifyAuthor = false,
              }) {
                _handleFeaturedAssignment(
                  photo,
                  action: action,
                  notifyAuthor: notifyAuthor,
                );
              },
          onPotdAssignment: ({required AssignmentActionType action}) {
            _handlePotdAssignment(photo, action: action);
          },
        );
      },
    );
  }

  void _startProcessing() {
    _isProcessingNotifier.value = true;
  }

  void _stopProcessing({VoidCallback? callback}) {
    _isProcessingNotifier.value = false;
    if (mounted) callback?.call();
  }

  void _handlePhotoArchiveAssignment({
    required PhotoData photo,
    required AssignmentActionType actionType,
    VoidCallback? onArchivedStatusChanged,
  }) async {
    ArchiveAssignmentUtil(
      ref: ref,
      photo: photo,
      startLoading: _startProcessing,
      stopLoading: _stopProcessing,
      onSuccess: (String msg) {
        _showSuccessSnackBar(msg);
        onArchivedStatusChanged?.call();
      },
      onSessionEndedError: _showSessionEndedDialog,
      onError: _showErrorSnackBar,
    ).handleAssignment(actionType: actionType);
  }

  void _handleDeletePhoto(PhotoData photo, {bool isModeration = false}) async {
    PhotoDeletionUtil(
      context: context,
      ref: ref,
      photo: photo,
      isOwnPhoto: _isOwnPhoto,
      startLoading: _startProcessing,
      stopLoading: _stopProcessing,
    ).handleDeletion(isPhotoReportModeration: isModeration);
  }

  void _handleFeaturedAssignment(
    PhotoData photo, {
    required AssignmentActionType action,
    bool notifyAuthor = false,
  }) async {
    FeaturedAssignmentUtil(
      context: context,
      ref: ref,
      photo: photo,
      startLoading: _startProcessing,
      stopLoading: _stopProcessing,
    ).handleAssignment(action: action, notifyAuthor: notifyAuthor);
  }

  void _handlePotdAssignment(
    PhotoData photo, {
    required AssignmentActionType action,
  }) async {
    PotdAssignmentUtil(
      context: context,
      ref: ref,
      photo: photo,
      startLoading: _startProcessing,
      stopLoading: _stopProcessing,
    ).handleAssignment(action: action);
  }

  void _showSuccessSnackBar(String msg) {
    showAppSnackBar(
      context: context,
      message: msg,
      type: AppSnackBarType.success,
    );
  }

  void _showSessionEndedDialog() {
    showSessionEndedDialog(context, ref);
  }

  void _showErrorSnackBar(String msg) {
    showAppSnackBar(context: context, message: msg);
  }
}
